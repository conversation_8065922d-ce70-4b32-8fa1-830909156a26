'use client'

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/auth-context';
import { UserRole, AdminPermission, RoutePermission } from '@/types/auth';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRoles?: UserRole[];
  requiredPermissions?: AdminPermission[];
  requireAll?: boolean;
  fallbackPath?: string;
}

export function ProtectedRoute({
  children,
  requiredRoles = [],
  requiredPermissions = [],
  requireAll = false,
  fallbackPath = '/login'
}: ProtectedRouteProps) {
  const { user, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (isLoading) return;

    if (!isAuthenticated || !user) {
      router.push(fallbackPath);
      return;
    }

    // Check role requirements
    if (requiredRoles.length > 0) {
      const hasRequiredRole = requiredRoles.includes(user.role);
      if (!hasRequiredRole) {
        router.push('/unauthorized');
        return;
      }
    }

    // Check permission requirements
    if (requiredPermissions.length > 0) {
      const userPermissions = getUserPermissions(user);
      
      if (requireAll) {
        // User must have ALL required permissions
        const hasAllPermissions = requiredPermissions.every(permission =>
          userPermissions.includes(permission)
        );
        if (!hasAllPermissions) {
          router.push('/unauthorized');
          return;
        }
      } else {
        // User must have ANY of the required permissions
        const hasAnyPermission = requiredPermissions.some(permission =>
          userPermissions.includes(permission)
        );
        if (!hasAnyPermission) {
          router.push('/unauthorized');
          return;
        }
      }
    }
  }, [isAuthenticated, user, isLoading, router, requiredRoles, requiredPermissions, requireAll, fallbackPath]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!isAuthenticated || !user) {
    return null;
  }

  return <>{children}</>;
}

function getUserPermissions(user: any): AdminPermission[] {
  // Get permissions based on user role and specific admin permissions
  const rolePermissions: Record<UserRole, AdminPermission[]> = {
    patient: [],
    medical_staff: ['patient_data_access'],
    admin: [
      'user_management',
      'system_config',
      'data_analytics',
      'billing',
      'audit_logs',
      'patient_data_access',
      'staff_management',
      'ai_configuration'
    ]
  };

  let permissions = rolePermissions[user.role] || [];

  // Add specific admin permissions if user has adminData
  if (user.role === 'admin' && user.adminData?.permissions) {
    permissions = [...new Set([...permissions, ...user.adminData.permissions])];
  }

  return permissions;
}
