import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { 
  User, 
  AuthState, 
  LoginCredentials, 
  RegisterData, 
  UserRole, 
  AdminPermission,
  ROLE_PERMISSIONS 
} from '@/types/auth';

interface AuthStore extends AuthState {
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => void;
  clearError: () => void;
  hasPermission: (permission: AdminPermission) => boolean;
  hasRole: (role: UserRole) => boolean;
  setUser: (user: User) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

// Mock authentication service - replace with real API calls
const mockAuthService = {
  async login(credentials: LoginCredentials): Promise<User> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock users for testing
    const mockUsers: Record<string, User> = {
      '<EMAIL>': {
        id: '1',
        email: '<EMAIL>',
        name: '<PERSON>',
        role: 'patient',
        createdAt: new Date(),
        lastLogin: new Date(),
        isActive: true,
        patientData: {
          patientId: 'P001',
          dateOfBirth: new Date('1980-01-01'),
          phoneNumber: '+**********',
          emergencyContact: {
            name: 'Jane Patient',
            phone: '+**********',
            relationship: 'Spouse'
          },
          medicalConditions: ['Hypertension', 'Diabetes Type 2'],
          medications: ['Metformin', 'Lisinopril'],
          allergies: ['Penicillin'],
          preferredLanguage: 'English',
          communicationPreferences: {
            voiceCalls: true,
            textMessages: true,
            email: false
          }
        }
      },
      '<EMAIL>': {
        id: '2',
        email: '<EMAIL>',
        name: 'Dr. Sarah Staff',
        role: 'medical_staff',
        createdAt: new Date(),
        lastLogin: new Date(),
        isActive: true,
        staffData: {
          staffId: 'S001',
          department: 'Cardiology',
          position: 'Physician',
          licenseNumber: 'MD123456',
          specializations: ['Cardiology', 'Internal Medicine'],
          assignedPatients: ['P001', 'P002', 'P003'],
          workSchedule: {
            monday: { start: '08:00', end: '17:00' },
            tuesday: { start: '08:00', end: '17:00' },
            wednesday: { start: '08:00', end: '17:00' },
            thursday: { start: '08:00', end: '17:00' },
            friday: { start: '08:00', end: '17:00' },
            saturday: null,
            sunday: null
          }
        }
      },
      '<EMAIL>': {
        id: '3',
        email: '<EMAIL>',
        name: 'Admin User',
        role: 'admin',
        createdAt: new Date(),
        lastLogin: new Date(),
        isActive: true,
        adminData: {
          adminId: 'A001',
          permissions: [
            'user_management',
            'system_config',
            'data_analytics',
            'billing',
            'audit_logs',
            'patient_data_access',
            'staff_management',
            'ai_configuration'
          ],
          managedDepartments: ['All'],
          systemAccess: {
            userManagement: true,
            systemConfiguration: true,
            dataAnalytics: true,
            billing: true,
            auditLogs: true
          }
        }
      }
    };

    const user = mockUsers[credentials.email];
    if (!user || credentials.password !== 'password') {
      throw new Error('Invalid email or password');
    }

    return { ...user, lastLogin: new Date() };
  },

  async register(data: RegisterData): Promise<User> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock registration - in real app, this would create user in database
    const newUser: User = {
      id: Math.random().toString(36).substr(2, 9),
      email: data.email,
      name: data.name,
      role: data.role,
      createdAt: new Date(),
      isActive: true
    };

    // Add role-specific data based on registration
    if (data.role === 'patient' && data.additionalData) {
      newUser.patientData = data.additionalData as any;
    } else if (data.role === 'medical_staff' && data.additionalData) {
      newUser.staffData = data.additionalData as any;
    } else if (data.role === 'admin' && data.additionalData) {
      newUser.adminData = data.additionalData as any;
    }

    return newUser;
  }
};

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      login: async (credentials: LoginCredentials) => {
        set({ isLoading: true, error: null });
        try {
          const user = await mockAuthService.login(credentials);
          set({ 
            user, 
            isAuthenticated: true, 
            isLoading: false,
            error: null 
          });
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Login failed',
            isLoading: false 
          });
          throw error;
        }
      },

      register: async (data: RegisterData) => {
        set({ isLoading: true, error: null });
        try {
          const user = await mockAuthService.register(data);
          set({ 
            user, 
            isAuthenticated: true, 
            isLoading: false,
            error: null 
          });
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Registration failed',
            isLoading: false 
          });
          throw error;
        }
      },

      logout: () => {
        set({ 
          user: null, 
          isAuthenticated: false, 
          error: null 
        });
      },

      clearError: () => {
        set({ error: null });
      },

      hasPermission: (permission: AdminPermission) => {
        const { user } = get();
        if (!user) return false;
        
        const rolePermissions = ROLE_PERMISSIONS[user.role];
        return rolePermissions.includes(permission);
      },

      hasRole: (role: UserRole) => {
        const { user } = get();
        return user?.role === role;
      },

      setUser: (user: User) => {
        set({ user, isAuthenticated: true });
      },

      setLoading: (isLoading: boolean) => {
        set({ isLoading });
      },

      setError: (error: string | null) => {
        set({ error });
      }
    }),
    {
      name: 'phoenixcare-auth',
      partialize: (state) => ({ 
        user: state.user, 
        isAuthenticated: state.isAuthenticated 
      }),
    }
  )
);
