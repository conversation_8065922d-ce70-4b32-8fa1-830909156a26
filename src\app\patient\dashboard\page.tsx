'use client'

import { useState } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { VoiceCallInterface } from '@/components/voice/voice-call-interface';
import { 
  Heart, 
  MessageCircle, 
  Calendar, 
  Pill, 
  Phone,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';

export default function PatientDashboard() {
  const { user } = useAuth();
  const [showVoiceCall, setShowVoiceCall] = useState(false);

  const patientData = user?.patientData;

  const upcomingAppointments = [
    {
      id: '1',
      type: 'Follow-up',
      doctor: 'Dr. <PERSON>',
      date: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
      time: '10:00 AM'
    },
    {
      id: '2',
      type: 'Lab Results Review',
      doctor: 'Dr. <PERSON>',
      date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      time: '2:30 PM'
    }
  ];

  const recentSymptoms = [
    { symptom: 'Mild fatigue', severity: 3, date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000) },
    { symptom: 'Slight shortness of breath', severity: 2, date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000) }
  ];

  const medicationReminders = [
    { name: 'Metformin', time: '8:00 AM', taken: true },
    { name: 'Lisinopril', time: '8:00 AM', taken: true },
    { name: 'Metformin', time: '6:00 PM', taken: false }
  ];

  if (showVoiceCall) {
    return (
      <ProtectedRoute requiredRoles={['patient']}>
        <div className="min-h-screen bg-background p-4">
          <div className="max-w-4xl mx-auto">
            <div className="mb-6">
              <Button 
                variant="outline" 
                onClick={() => setShowVoiceCall(false)}
                className="mb-4"
              >
                ← Back to Dashboard
              </Button>
              <h1 className="text-2xl font-bold">AI Health Assistant</h1>
              <p className="text-muted-foreground">
                Connect with your AI health assistant for check-ins and support
              </p>
            </div>
            
            <VoiceCallInterface
              patientId={user?.id || ''}
              patientName={user?.name || 'Patient'}
              onCallEnd={() => setShowVoiceCall(false)}
            />
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute requiredRoles={['patient']}>
      <div className="min-h-screen bg-background">
        {/* Header */}
        <header className="border-b bg-card">
          <div className="container mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold">Welcome back, {user?.name}</h1>
                <p className="text-sm text-muted-foreground">
                  Your health dashboard and AI assistant
                </p>
              </div>
              <Button 
                onClick={() => setShowVoiceCall(true)}
                className="bg-primary hover:bg-primary/90"
              >
                <MessageCircle className="w-4 h-4 mr-2" />
                Talk to AI Assistant
              </Button>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="container mx-auto px-4 py-8">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {/* Health Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Heart className="w-5 h-5 text-red-500" />
                  <span>Health Status</span>
                </CardTitle>
                <CardDescription>Your current health overview</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Overall Status</span>
                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                      Stable
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Last Check-in</span>
                    <span className="text-sm text-muted-foreground">2 days ago</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Next AI Call</span>
                    <span className="text-sm text-muted-foreground">Tomorrow</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>Common tasks and features</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button 
                  variant="outline" 
                  className="w-full justify-start"
                  onClick={() => setShowVoiceCall(true)}
                >
                  <Phone className="w-4 h-4 mr-2" />
                  Start Voice Check-in
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Calendar className="w-4 h-4 mr-2" />
                  View Appointments
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Pill className="w-4 h-4 mr-2" />
                  Medication Tracker
                </Button>
              </CardContent>
            </Card>

            {/* Recent Symptoms */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <AlertCircle className="w-5 h-5 text-yellow-500" />
                  <span>Recent Symptoms</span>
                </CardTitle>
                <CardDescription>Symptoms reported in the last week</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {recentSymptoms.map((symptom, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium">{symptom.symptom}</p>
                        <p className="text-xs text-muted-foreground">
                          {symptom.date.toLocaleDateString()}
                        </p>
                      </div>
                      <Badge 
                        variant={symptom.severity <= 2 ? "secondary" : symptom.severity <= 5 ? "default" : "destructive"}
                      >
                        {symptom.severity}/10
                      </Badge>
                    </div>
                  ))}
                  {recentSymptoms.length === 0 && (
                    <p className="text-sm text-muted-foreground">No symptoms reported</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Upcoming Appointments */}
          <div className="mt-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Calendar className="w-5 h-5 text-blue-500" />
                  <span>Upcoming Appointments</span>
                </CardTitle>
                <CardDescription>Your scheduled medical appointments</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {upcomingAppointments.map((appointment) => (
                    <div key={appointment.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <p className="font-medium">{appointment.type}</p>
                        <p className="text-sm text-muted-foreground">with {appointment.doctor}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">
                          {appointment.date.toLocaleDateString()}
                        </p>
                        <p className="text-sm text-muted-foreground">{appointment.time}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Medication Reminders */}
          <div className="mt-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Pill className="w-5 h-5 text-green-500" />
                  <span>Today's Medications</span>
                </CardTitle>
                <CardDescription>Your medication schedule for today</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {medicationReminders.map((med, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        {med.taken ? (
                          <CheckCircle className="w-5 h-5 text-green-500" />
                        ) : (
                          <Clock className="w-5 h-5 text-yellow-500" />
                        )}
                        <div>
                          <p className="font-medium">{med.name}</p>
                          <p className="text-sm text-muted-foreground">{med.time}</p>
                        </div>
                      </div>
                      <Badge variant={med.taken ? "secondary" : "default"}>
                        {med.taken ? 'Taken' : 'Pending'}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </ProtectedRoute>
  );
}
