export type UserRole = 'patient' | 'medical_staff' | 'admin';

export interface User {
  id: string;
  email: string;
  name: string;
  role: User<PERSON><PERSON>;
  avatar?: string;
  createdAt: Date;
  lastLogin?: Date;
  isActive: boolean;
  // Role-specific data
  patientData?: PatientProfile;
  staffData?: StaffProfile;
  adminData?: AdminProfile;
}

export interface PatientProfile {
  patientId: string;
  dateOfBirth: Date;
  phoneNumber: string;
  emergencyContact: {
    name: string;
    phone: string;
    relationship: string;
  };
  medicalConditions: string[];
  medications: string[];
  allergies: string[];
  preferredLanguage: string;
  communicationPreferences: {
    voiceCalls: boolean;
    textMessages: boolean;
    email: boolean;
  };
}

export interface StaffProfile {
  staffId: string;
  department: string;
  position: string;
  licenseNumber?: string;
  specializations: string[];
  assignedPatients: string[];
  workSchedule: {
    monday: { start: string; end: string } | null;
    tuesday: { start: string; end: string } | null;
    wednesday: { start: string; end: string } | null;
    thursday: { start: string; end: string } | null;
    friday: { start: string; end: string } | null;
    saturday: { start: string; end: string } | null;
    sunday: { start: string; end: string } | null;
  };
}

export interface AdminProfile {
  adminId: string;
  permissions: AdminPermission[];
  managedDepartments: string[];
  systemAccess: {
    userManagement: boolean;
    systemConfiguration: boolean;
    dataAnalytics: boolean;
    billing: boolean;
    auditLogs: boolean;
  };
}

export type AdminPermission = 
  | 'user_management'
  | 'system_config'
  | 'data_analytics'
  | 'billing'
  | 'audit_logs'
  | 'patient_data_access'
  | 'staff_management'
  | 'ai_configuration';

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  name: string;
  role: UserRole;
  additionalData?: Partial<PatientProfile | StaffProfile | AdminProfile>;
}

export interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => void;
  clearError: () => void;
  hasPermission: (permission: string) => boolean;
  hasRole: (role: UserRole) => boolean;
}

// Route protection types
export interface RoutePermission {
  roles: UserRole[];
  permissions?: AdminPermission[];
  requireAll?: boolean; // If true, user must have ALL permissions, otherwise ANY
}

export const ROLE_PERMISSIONS: Record<UserRole, AdminPermission[]> = {
  patient: [],
  medical_staff: ['patient_data_access'],
  admin: [
    'user_management',
    'system_config',
    'data_analytics',
    'billing',
    'audit_logs',
    'patient_data_access',
    'staff_management',
    'ai_configuration'
  ]
};

// Navigation items based on role
export interface NavigationItem {
  label: string;
  href: string;
  icon?: string;
  roles: UserRole[];
  permissions?: AdminPermission[];
}

export const NAVIGATION_ITEMS: NavigationItem[] = [
  // Patient navigation
  {
    label: 'My Health',
    href: '/patient/dashboard',
    icon: 'Heart',
    roles: ['patient']
  },
  {
    label: 'AI Assistant',
    href: '/patient/assistant',
    icon: 'MessageCircle',
    roles: ['patient']
  },
  {
    label: 'Appointments',
    href: '/patient/appointments',
    icon: 'Calendar',
    roles: ['patient']
  },
  {
    label: 'Medical History',
    href: '/patient/history',
    icon: 'FileText',
    roles: ['patient']
  },
  
  // Medical staff navigation
  {
    label: 'Patient Dashboard',
    href: '/staff/patients',
    icon: 'Users',
    roles: ['medical_staff', 'admin']
  },
  {
    label: 'Voice Calls',
    href: '/staff/calls',
    icon: 'Phone',
    roles: ['medical_staff', 'admin']
  },
  {
    label: 'AI Conversations',
    href: '/staff/conversations',
    icon: 'MessageSquare',
    roles: ['medical_staff', 'admin']
  },
  {
    label: 'Reports',
    href: '/staff/reports',
    icon: 'BarChart3',
    roles: ['medical_staff', 'admin']
  },
  
  // Admin navigation
  {
    label: 'User Management',
    href: '/admin/users',
    icon: 'UserCog',
    roles: ['admin'],
    permissions: ['user_management']
  },
  {
    label: 'System Config',
    href: '/admin/config',
    icon: 'Settings',
    roles: ['admin'],
    permissions: ['system_config']
  },
  {
    label: 'Analytics',
    href: '/admin/analytics',
    icon: 'TrendingUp',
    roles: ['admin'],
    permissions: ['data_analytics']
  },
  {
    label: 'Billing',
    href: '/admin/billing',
    icon: 'CreditCard',
    roles: ['admin'],
    permissions: ['billing']
  }
];
