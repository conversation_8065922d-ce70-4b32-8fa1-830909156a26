import { create } from 'zustand';
import { 
  VoiceCallStore, 
  VoiceCallState, 
  VoiceCallSession, 
  CallType,
  AudioDevice,
  AudioSettings,
  ConnectionQuality
} from '@/types/voice';

const initialAudioSettings: AudioSettings = {
  echoCancellation: true,
  noiseSuppression: true,
  autoGainControl: true,
  volume: 0.8,
  muted: false
};

const initialConnectionQuality: ConnectionQuality = {
  latency: 0,
  packetLoss: 0,
  jitter: 0,
  bandwidth: 0,
  signalStrength: 'good'
};

const initialState: VoiceCallState = {
  session: null,
  isConnecting: false,
  isConnected: false,
  isMuted: false,
  isRecording: false,
  audioDevices: [],
  audioSettings: initialAudioSettings,
  connectionQuality: initialConnectionQuality,
  transcript: '',
  error: null,
  staffJoined: false,
  aiResponding: false
};

export const useVoiceCallStore = create<VoiceCallStore>((set, get) => ({
  state: initialState,

  controls: {
    startCall: async (patientId: string, type: CallType) => {
      const { createSession } = get();
      set(state => ({
        state: {
          ...state.state,
          isConnecting: true,
          error: null
        }
      }));

      try {
        const session = createSession(patientId, type);
        
        // Initialize WebRTC connection
        await initializeWebRTCConnection();
        
        set(state => ({
          state: {
            ...state.state,
            session,
            isConnecting: false,
            isConnected: true
          }
        }));
      } catch (error) {
        set(state => ({
          state: {
            ...state.state,
            isConnecting: false,
            error: error instanceof Error ? error.message : 'Failed to start call'
          }
        }));
        throw error;
      }
    },

    endCall: async () => {
      const { endSession } = get();
      
      try {
        // Clean up WebRTC connection
        await cleanupWebRTCConnection();
        
        endSession();
        
        set(state => ({
          state: {
            ...state.state,
            isConnected: false,
            isConnecting: false,
            session: null,
            transcript: '',
            staffJoined: false,
            aiResponding: false
          }
        }));
      } catch (error) {
        console.error('Error ending call:', error);
      }
    },

    toggleMute: () => {
      set(state => ({
        state: {
          ...state.state,
          isMuted: !state.state.isMuted,
          audioSettings: {
            ...state.state.audioSettings,
            muted: !state.state.isMuted
          }
        }
      }));
    },

    toggleRecording: () => {
      set(state => ({
        state: {
          ...state.state,
          isRecording: !state.state.isRecording
        }
      }));
    },

    adjustVolume: (volume: number) => {
      set(state => ({
        state: {
          ...state.state,
          audioSettings: {
            ...state.state.audioSettings,
            volume: Math.max(0, Math.min(1, volume))
          }
        }
      }));
    },

    switchAudioDevice: async (deviceId: string, type: 'input' | 'output') => {
      const { updateAudioSettings } = get();
      
      if (type === 'input') {
        updateAudioSettings({ inputDevice: deviceId });
      } else {
        updateAudioSettings({ outputDevice: deviceId });
      }
      
      // Apply device change to active stream
      await applyAudioDeviceChange(deviceId, type);
    },

    joinAsStaff: async (staffId: string) => {
      set(state => ({
        state: {
          ...state.state,
          staffJoined: true
        }
      }));
      
      // Notify other participants
      await notifyStaffJoined(staffId);
    },

    leaveAsStaff: () => {
      set(state => ({
        state: {
          ...state.state,
          staffJoined: false
        }
      }));
    },

    sendTextMessage: (message: string) => {
      // Implementation for sending text messages during call
      console.log('Sending text message:', message);
    },

    requestEmergencyAssistance: () => {
      // Implementation for emergency assistance
      console.log('Emergency assistance requested');
    }
  },

  setState: (updates: Partial<VoiceCallState>) => {
    set(state => ({
      state: { ...state.state, ...updates }
    }));
  },

  resetState: () => {
    set({ state: initialState });
  },

  initializeAudioDevices: async () => {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      const audioDevices: AudioDevice[] = devices
        .filter(device => device.kind === 'audioinput' || device.kind === 'audiooutput')
        .map(device => ({
          deviceId: device.deviceId,
          label: device.label || `${device.kind} ${device.deviceId.slice(0, 8)}`,
          kind: device.kind as 'audioinput' | 'audiooutput'
        }));

      set(state => ({
        state: {
          ...state.state,
          audioDevices
        }
      }));
    } catch (error) {
      console.error('Failed to enumerate audio devices:', error);
    }
  },

  updateAudioSettings: (settings: Partial<AudioSettings>) => {
    set(state => ({
      state: {
        ...state.state,
        audioSettings: {
          ...state.state.audioSettings,
          ...settings
        }
      }
    }));
  },

  updateConnectionQuality: (quality: Partial<ConnectionQuality>) => {
    set(state => ({
      state: {
        ...state.state,
        connectionQuality: {
          ...state.state.connectionQuality,
          ...quality
        }
      }
    }));
  },

  handleConnectionError: (error: string) => {
    set(state => ({
      state: {
        ...state.state,
        error,
        isConnected: false,
        isConnecting: false
      }
    }));
  },

  addTranscriptSegment: (text: string, speaker: 'user' | 'assistant' | 'staff') => {
    const timestamp = new Date().toLocaleTimeString();
    const segment = `[${timestamp}] ${speaker.toUpperCase()}: ${text}\n`;
    
    set(state => ({
      state: {
        ...state.state,
        transcript: state.state.transcript + segment
      }
    }));
  },

  clearTranscript: () => {
    set(state => ({
      state: {
        ...state.state,
        transcript: ''
      }
    }));
  },

  createSession: (patientId: string, type: CallType): VoiceCallSession => {
    const session: VoiceCallSession = {
      id: `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      patientId,
      type,
      status: 'connecting',
      startTime: new Date(),
      audioQuality: 'good',
      metadata: {
        patientName: `Patient ${patientId}`, // This would come from patient data
        patientCondition: 'Unknown', // This would come from patient data
        callPurpose: type,
        urgencyLevel: 'medium',
        connectionAttempts: 1,
        remindersSent: 0,
        technicalIssues: []
      }
    };

    return session;
  },

  updateSession: (updates: Partial<VoiceCallSession>) => {
    set(state => ({
      state: {
        ...state.state,
        session: state.state.session ? {
          ...state.state.session,
          ...updates
        } : null
      }
    }));
  },

  endSession: () => {
    const { state } = get();
    if (state.session) {
      const endTime = new Date();
      const duration = Math.floor((endTime.getTime() - state.session.startTime.getTime()) / 1000);
      
      set(currentState => ({
        state: {
          ...currentState.state,
          session: {
            ...state.session!,
            status: 'disconnecting',
            endTime,
            duration
          }
        }
      }));
    }
  }
}));

// Helper functions for WebRTC operations
async function initializeWebRTCConnection(): Promise<void> {
  // Implementation for WebRTC initialization
  console.log('Initializing WebRTC connection...');
  
  // Request microphone permissions
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ 
      audio: true, 
      video: false 
    });
    
    // Store the stream for later use
    (window as any).currentAudioStream = stream;
    
    console.log('Audio stream acquired');
  } catch (error) {
    throw new Error('Failed to access microphone');
  }
}

async function cleanupWebRTCConnection(): Promise<void> {
  // Implementation for WebRTC cleanup
  console.log('Cleaning up WebRTC connection...');
  
  const stream = (window as any).currentAudioStream;
  if (stream) {
    stream.getTracks().forEach((track: MediaStreamTrack) => track.stop());
    (window as any).currentAudioStream = null;
  }
}

async function applyAudioDeviceChange(deviceId: string, type: 'input' | 'output'): Promise<void> {
  // Implementation for changing audio devices
  console.log(`Switching ${type} device to:`, deviceId);
}

async function notifyStaffJoined(staffId: string): Promise<void> {
  // Implementation for notifying participants about staff joining
  console.log('Staff joined call:', staffId);
}
