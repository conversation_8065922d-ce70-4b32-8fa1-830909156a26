'use client'

import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertTriangle } from 'lucide-react';

export default function UnauthorizedPage() {
  const { user, logout } = useAuth();
  const router = useRouter();

  const handleGoBack = () => {
    if (user) {
      switch (user.role) {
        case 'patient':
          router.push('/patient/dashboard');
          break;
        case 'medical_staff':
          router.push('/');
          break;
        case 'admin':
          router.push('/');
          break;
        default:
          router.push('/login');
      }
    } else {
      router.push('/login');
    }
  };

  const handleLogout = () => {
    logout();
    router.push('/login');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-2xl font-bold text-gray-900">
              Access Denied
            </CardTitle>
            <CardDescription className="mt-2">
              You don't have permission to access this page.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center text-sm text-gray-600">
              <p>Your current role: <span className="font-medium">{user?.role || 'Unknown'}</span></p>
              <p className="mt-1">
                This page requires different permissions than what your account has.
              </p>
            </div>
            
            <div className="flex flex-col space-y-2">
              <Button onClick={handleGoBack} className="w-full">
                Go to Dashboard
              </Button>
              <Button onClick={handleLogout} variant="outline" className="w-full">
                Sign Out
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
