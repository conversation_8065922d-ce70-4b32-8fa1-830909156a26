'use client'

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { PatientList } from "@/components/patients/patient-list";
import { getPatientSummaries } from "@/lib/mock-data";
import { useState } from "react";

export default function Home() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const [patients] = useState(() => getPatientSummaries());
  const [selectedPatientId, setSelectedPatientId] = useState<string | null>(null);

  // Redirect based on user role
  useEffect(() => {
    if (isLoading) return;

    if (!isAuthenticated) {
      router.push('/login');
      return;
    }

    if (user) {
      switch (user.role) {
        case 'patient':
          router.push('/patient/dashboard');
          break;
        case 'medical_staff':
          // Stay on this page (staff dashboard)
          break;
        case 'admin':
          // Stay on this page (admin can see everything)
          break;
        default:
          router.push('/login');
      }
    }
  }, [user, isAuthenticated, isLoading, router]);

  const handlePatientSelect = (patientId: string) => {
    setSelectedPatientId(patientId);
    // TODO: Navigate to patient detail page or open modal
    console.log('Selected patient:', patientId, 'Current selection:', selectedPatientId);
  };

  const handleStartCall = (patientId: string) => {
    // TODO: Implement voice call functionality
    console.log('Starting call with patient:', patientId);
  };

  const activePatients = patients.filter(p => p.patient.status === 'active');
  const totalAlerts = patients.reduce((sum, p) => sum + p.activeAlerts.length, 0);
  const completedCallsToday = patients.reduce((sum, p) =>
    sum + p.recentCalls.filter(call =>
      call.status === 'completed' &&
      new Date(call.startTime).toDateString() === new Date().toDateString()
    ).length, 0
  );

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Show login redirect message
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Redirecting to login...</h2>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
        </div>
      </div>
    );
  }

  // Only show this page for medical staff and admin
  if (user?.role === 'patient') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Redirecting to your dashboard...</h2>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-card">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-primary">PhoenixCare</h1>
              <span className="text-sm text-muted-foreground">AI-Powered Post-Discharge Care</span>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="outline">Settings</Button>
              <Button>New Patient</Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-8">
          {/* Dashboard Overview */}
          <Card>
            <CardHeader>
              <CardTitle>Active Patients</CardTitle>
              <CardDescription>Patients requiring monitoring</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{activePatients.length}</div>
              <p className="text-sm text-muted-foreground">Currently active</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Voice Calls Today</CardTitle>
              <CardDescription>AI-powered patient check-ins</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{completedCallsToday}</div>
              <p className="text-sm text-muted-foreground">Completed calls</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Priority Alerts</CardTitle>
              <CardDescription>Patients needing immediate attention</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-destructive">{totalAlerts}</div>
              <p className="text-sm text-muted-foreground">Active alerts</p>
            </CardContent>
          </Card>
        </div>

        {/* Patient List */}
        <div>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold">Patient List</h2>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">Filter</Button>
              <Button variant="outline" size="sm">Sort</Button>
            </div>
          </div>
          <PatientList
            patients={patients}
            onPatientSelect={handlePatientSelect}
            onStartCall={handleStartCall}
          />
        </div>
      </main>
    </div>
  );
}
