import { OpenAIRealtimeConfig, RealtimeEvent, ConversationItem } from '@/types/voice';

export class OpenAIRealtimeService {
  private ws: WebSocket | null = null;
  private config: OpenAIRealtimeConfig;
  private isConnected = false;
  private eventListeners: Map<string, Function[]> = new Map();
  private conversationItems: ConversationItem[] = [];

  constructor(config: OpenAIRealtimeConfig) {
    this.config = config;
  }

  async connect(): Promise<void> {
    if (this.isConnected) {
      console.warn('Already connected to OpenAI Realtime API');
      return;
    }

    try {
      // Note: In production, you'd need to handle authentication properly
      // This is a simplified version for demonstration
      const wsUrl = 'wss://api.openai.com/v1/realtime?model=' + this.config.model;
      
      this.ws = new WebSocket(wsUrl, [], {
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'OpenAI-Beta': 'realtime=v1'
        }
      } as any);

      this.ws.onopen = this.handleOpen.bind(this);
      this.ws.onmessage = this.handleMessage.bind(this);
      this.ws.onclose = this.handleClose.bind(this);
      this.ws.onerror = this.handleError.bind(this);

      // Wait for connection
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Connection timeout'));
        }, 10000);

        this.ws!.onopen = () => {
          clearTimeout(timeout);
          this.handleOpen();
          resolve(void 0);
        };

        this.ws!.onerror = (error) => {
          clearTimeout(timeout);
          reject(error);
        };
      });

    } catch (error) {
      console.error('Failed to connect to OpenAI Realtime API:', error);
      throw error;
    }
  }

  disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.isConnected = false;
    this.emit('disconnected');
  }

  private handleOpen(): void {
    console.log('Connected to OpenAI Realtime API');
    this.isConnected = true;
    
    // Send session configuration
    this.sendEvent({
      type: 'session.update',
      session: {
        modalities: this.config.modalities,
        instructions: this.config.instructions,
        voice: this.config.voice,
        input_audio_format: 'pcm16',
        output_audio_format: 'pcm16',
        input_audio_transcription: {
          model: 'whisper-1'
        },
        turn_detection: {
          type: 'server_vad',
          threshold: 0.5,
          prefix_padding_ms: 300,
          silence_duration_ms: 200
        },
        tools: [],
        tool_choice: 'auto',
        temperature: this.config.temperature,
        max_response_output_tokens: this.config.maxTokens || 4096
      }
    });

    this.emit('connected');
  }

  private handleMessage(event: MessageEvent): void {
    try {
      const data: RealtimeEvent = JSON.parse(event.data);
      console.log('Received event:', data.type, data);
      
      this.handleRealtimeEvent(data);
      this.emit('event', data);
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error);
    }
  }

  private handleClose(event: CloseEvent): void {
    console.log('Disconnected from OpenAI Realtime API:', event.code, event.reason);
    this.isConnected = false;
    this.emit('disconnected', { code: event.code, reason: event.reason });
  }

  private handleError(error: Event): void {
    console.error('WebSocket error:', error);
    this.emit('error', error);
  }

  private handleRealtimeEvent(event: RealtimeEvent): void {
    switch (event.type) {
      case 'session.created':
        this.emit('session_created', event);
        break;
        
      case 'session.updated':
        this.emit('session_updated', event);
        break;
        
      case 'conversation.item.created':
        this.addConversationItem(event.item);
        this.emit('conversation_item_created', event);
        break;
        
      case 'conversation.item.input_audio_transcription.completed':
        this.updateTranscription(event.item_id, event.transcript);
        this.emit('transcription_completed', event);
        break;
        
      case 'response.created':
        this.emit('response_created', event);
        break;
        
      case 'response.output_item.added':
        this.emit('response_output_item_added', event);
        break;
        
      case 'response.content_part.added':
        this.emit('response_content_part_added', event);
        break;
        
      case 'response.audio.delta':
        this.emit('audio_delta', event);
        break;
        
      case 'response.audio.done':
        this.emit('audio_done', event);
        break;
        
      case 'response.text.delta':
        this.emit('text_delta', event);
        break;
        
      case 'response.text.done':
        this.emit('text_done', event);
        break;
        
      case 'response.done':
        this.emit('response_done', event);
        break;
        
      case 'error':
        console.error('OpenAI Realtime API error:', event);
        this.emit('api_error', event);
        break;
        
      default:
        console.log('Unhandled event type:', event.type);
    }
  }

  sendEvent(event: RealtimeEvent): void {
    if (!this.isConnected || !this.ws) {
      console.warn('Cannot send event: not connected');
      return;
    }

    try {
      this.ws.send(JSON.stringify(event));
    } catch (error) {
      console.error('Failed to send event:', error);
    }
  }

  sendAudioData(audioData: ArrayBuffer): void {
    if (!this.isConnected) {
      console.warn('Cannot send audio: not connected');
      return;
    }

    // Convert audio data to base64
    const base64Audio = this.arrayBufferToBase64(audioData);
    
    this.sendEvent({
      type: 'input_audio_buffer.append',
      audio: base64Audio
    });
  }

  commitAudioBuffer(): void {
    this.sendEvent({
      type: 'input_audio_buffer.commit'
    });
  }

  clearAudioBuffer(): void {
    this.sendEvent({
      type: 'input_audio_buffer.clear'
    });
  }

  sendTextMessage(text: string): void {
    this.sendEvent({
      type: 'conversation.item.create',
      item: {
        type: 'message',
        role: 'user',
        content: [
          {
            type: 'input_text',
            text: text
          }
        ]
      }
    });

    this.sendEvent({
      type: 'response.create'
    });
  }

  generateResponse(): void {
    this.sendEvent({
      type: 'response.create'
    });
  }

  cancelResponse(): void {
    this.sendEvent({
      type: 'response.cancel'
    });
  }

  private addConversationItem(item: any): void {
    const conversationItem: ConversationItem = {
      id: item.id,
      type: item.type,
      role: item.role,
      content: item.content,
      status: item.status || 'completed',
      timestamp: new Date()
    };

    this.conversationItems.push(conversationItem);
  }

  private updateTranscription(itemId: string, transcript: string): void {
    const item = this.conversationItems.find(item => item.id === itemId);
    if (item && item.content) {
      const audioContent = item.content.find(c => c.type === 'input_audio');
      if (audioContent) {
        audioContent.transcript = transcript;
      }
    }
  }

  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  }

  // Event emitter methods
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Error in event listener:', error);
        }
      });
    }
  }

  getConversationItems(): ConversationItem[] {
    return [...this.conversationItems];
  }

  clearConversation(): void {
    this.conversationItems = [];
  }

  isConnectedToAPI(): boolean {
    return this.isConnected;
  }
}
