/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/patient/dashboard/page";
exports.ids = ["app/patient/dashboard/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpatient%2Fdashboard%2Fpage&page=%2Fpatient%2Fdashboard%2Fpage&appPaths=%2Fpatient%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fpatient%2Fdashboard%2Fpage.tsx&appDir=D%3A%5CPhoenixCare%5Cphoenixcare-copilot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CPhoenixCare%5Cphoenixcare-copilot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpatient%2Fdashboard%2Fpage&page=%2Fpatient%2Fdashboard%2Fpage&appPaths=%2Fpatient%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fpatient%2Fdashboard%2Fpage.tsx&appDir=D%3A%5CPhoenixCare%5Cphoenixcare-copilot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CPhoenixCare%5Cphoenixcare-copilot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/app-render/interop-default */ \"(rsc)/./node_modules/next/dist/server/app-render/interop-default.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/experimental/ppr */ \"(rsc)/./node_modules/next/dist/server/lib/experimental/ppr.js\");\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/request/fallback-params */ \"(rsc)/./node_modules/next/dist/server/request/fallback-params.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/app-render/encryption-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/encryption-utils.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/lib/streaming-metadata */ \"(rsc)/./node_modules/next/dist/server/lib/streaming-metadata.js\");\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/app-render/action-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/action-utils.js\");\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/server/lib/server-action-request-meta */ \"(rsc)/./node_modules/next/dist/server/lib/server-action-request-meta.js\");\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/client/components/app-router-headers */ \"(rsc)/./node_modules/next/dist/client/components/app-router-headers.js\");\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/is-bot */ \"next/dist/shared/lib/router/utils/is-bot\");\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! next/dist/lib/fallback */ \"(rsc)/./node_modules/next/dist/lib/fallback.js\");\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! next/dist/server/render-result */ \"(rsc)/./node_modules/next/dist/server/render-result.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! next/dist/server/stream-utils/encoded-tags */ \"(rsc)/./node_modules/next/dist/server/stream-utils/encoded-tags.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! next/dist/server/send-payload */ \"(rsc)/./node_modules/next/dist/server/send-payload.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__);\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\");\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__);\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! next/dist/client/components/redirect-status-code */ \"(rsc)/./node_modules/next/dist/client/components/redirect-status-code.js\");\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\",\"handler\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/not-found.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/not-found.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/forbidden.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/forbidden.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/unauthorized.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/unauthorized.js\", 23));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/patient/dashboard/page.tsx */ \"(rsc)/./src/app/patient/dashboard/page.tsx\"));\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'patient',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"next/dist/client/components/builtin/global-error.js\"],\n'not-found': [module2, \"next/dist/client/components/builtin/not-found.js\"],\n'forbidden': [module3, \"next/dist/client/components/builtin/forbidden.js\"],\n'unauthorized': [module4, \"next/dist/client/components/builtin/unauthorized.js\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\"];\n\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/patient/dashboard/page\",\n        pathname: \"/patient/dashboard\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || ''\n});\nasync function handler(req, res, ctx) {\n    var _this;\n    let srcPage = \"/patient/dashboard/page\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const initialPostponed = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'postponed');\n    // TODO: replace with more specific flags\n    const minimalMode = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'minimalMode');\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, query, params, parsedUrl, pageIsDynamic, buildManifest, nextFontManifest, reactLoadableManifest, serverActionsManifest, clientReferenceManifest, subresourceIntegrityManifest, prerenderManifest, isDraftMode, resolvedPathname, revalidateOnlyGenerated, routerServerContext, nextConfig } = prepareResult;\n    const pathname = parsedUrl.pathname || '/';\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__.normalizeAppPath)(srcPage);\n    let { isOnDemandRevalidate } = prepareResult;\n    const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n    const isPrerendered = prerenderManifest.routes[resolvedPathname];\n    let isSSG = Boolean(prerenderInfo || isPrerendered || prerenderManifest.routes[normalizedSrcPage]);\n    const userAgent = req.headers['user-agent'] || '';\n    const botType = (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.getBotType)(userAgent);\n    const isHtmlBot = (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.isHtmlBotRequest)(req);\n    /**\n   * If true, this indicates that the request being made is for an app\n   * prefetch request.\n   */ const isPrefetchRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isPrefetchRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_ROUTER_PREFETCH_HEADER]);\n    // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n    const isRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.RSC_HEADER]);\n    const isPossibleServerAction = (0,next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__.getIsPossibleServerAction)(req);\n    /**\n   * If the route being rendered is an app page, and the ppr feature has been\n   * enabled, then the given route _could_ support PPR.\n   */ const couldSupportPPR = (0,next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__.checkIsAppPPREnabled)(nextConfig.experimental.ppr);\n    // When enabled, this will allow the use of the `?__nextppronly` query to\n    // enable debugging of the static shell.\n    const hasDebugStaticShellQuery =  false && 0;\n    // When enabled, this will allow the use of the `?__nextppronly` query\n    // to enable debugging of the fallback shell.\n    const hasDebugFallbackShellQuery = hasDebugStaticShellQuery && query.__nextppronly === 'fallback';\n    // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n    // prerender manifest and this is an app page.\n    const isRoutePPREnabled = couldSupportPPR && (((_this = prerenderManifest.routes[normalizedSrcPage] ?? prerenderManifest.dynamicRoutes[normalizedSrcPage]) == null ? void 0 : _this.renderingMode) === 'PARTIALLY_STATIC' || // Ideally we'd want to check the appConfig to see if this page has PPR\n    // enabled or not, but that would require plumbing the appConfig through\n    // to the server during development. We assume that the page supports it\n    // but only during development.\n    hasDebugStaticShellQuery && (routeModule.isDev === true || (routerServerContext == null ? void 0 : routerServerContext.experimentalTestProxy) === true));\n    const isDebugStaticShell = hasDebugStaticShellQuery && isRoutePPREnabled;\n    // We should enable debugging dynamic accesses when the static shell\n    // debugging has been enabled and we're also in development mode.\n    const isDebugDynamicAccesses = isDebugStaticShell && routeModule.isDev === true;\n    const isDebugFallbackShell = hasDebugFallbackShellQuery && isRoutePPREnabled;\n    // If we're in minimal mode, then try to get the postponed information from\n    // the request metadata. If available, use it for resuming the postponed\n    // render.\n    const minimalPostponed = isRoutePPREnabled ? initialPostponed : undefined;\n    // If PPR is enabled, and this is a RSC request (but not a prefetch), then\n    // we can use this fact to only generate the flight data for the request\n    // because we can't cache the HTML (as it's also dynamic).\n    const isDynamicRSCRequest = isRoutePPREnabled && isRSCRequest && !isPrefetchRSCRequest;\n    // Need to read this before it's stripped by stripFlightHeaders. We don't\n    // need to transfer it to the request meta because it's only read\n    // within this function; the static segment data should have already been\n    // generated, so we will always either return a static response or a 404.\n    const segmentPrefetchHeader = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'segmentPrefetchRSCRequest');\n    // TODO: investigate existing bug with shouldServeStreamingMetadata always\n    // being true for a revalidate due to modifying the base-server this.renderOpts\n    // when fixing this to correct logic it causes hydration issue since we set\n    // serveStreamingMetadata to true during export\n    let serveStreamingMetadata = !userAgent ? true : (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.shouldServeStreamingMetadata)(userAgent, nextConfig.htmlLimitedBots);\n    if (isHtmlBot && isRoutePPREnabled) {\n        isSSG = false;\n        serveStreamingMetadata = false;\n    }\n    // In development, we always want to generate dynamic HTML.\n    let supportsDynamicResponse = // If we're in development, we always support dynamic HTML, unless it's\n    // a data request, in which case we only produce static HTML.\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isSSG || // If this request has provided postponed data, it supports dynamic\n    // HTML.\n    typeof initialPostponed === 'string' || // If this is a dynamic RSC request, then this render supports dynamic\n    // HTML (it's dynamic).\n    isDynamicRSCRequest;\n    // When html bots request PPR page, perform the full dynamic rendering.\n    const shouldWaitOnAllReady = isHtmlBot && isRoutePPREnabled;\n    let ssgCacheKey = null;\n    if (!isDraftMode && isSSG && !supportsDynamicResponse && !isPossibleServerAction && !minimalPostponed && !isDynamicRSCRequest) {\n        ssgCacheKey = resolvedPathname;\n    }\n    // the staticPathKey differs from ssgCacheKey since\n    // ssgCacheKey is null in dev since we're always in \"dynamic\"\n    // mode in dev to bypass the cache, but we still need to honor\n    // dynamicParams = false in dev mode\n    let staticPathKey = ssgCacheKey;\n    if (!staticPathKey && routeModule.isDev) {\n        staticPathKey = resolvedPathname;\n    }\n    const ComponentMod = {\n        ...next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__,\n        tree,\n        pages,\n        GlobalError: (next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default()),\n        handler,\n        routeModule,\n        __next_app__\n    };\n    // Before rendering (which initializes component tree modules), we have to\n    // set the reference manifests to our global store so Server Action's\n    // encryption util can access to them at the top level of the page module.\n    if (serverActionsManifest && clientReferenceManifest) {\n        (0,next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__.setReferenceManifestsSingleton)({\n            page: srcPage,\n            clientReferenceManifest,\n            serverActionsManifest,\n            serverModuleMap: (0,next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__.createServerModuleMap)({\n                serverActionsManifest\n            })\n        });\n    }\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    try {\n        const invokeRouteModule = async (span, context)=>{\n            const nextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextRequest(req);\n            const nextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextResponse(res);\n            // TODO: adapt for putting the RDC inside the postponed data\n            // If we're in dev, and this isn't a prefetch or a server action,\n            // we should seed the resume data cache.\n            if (true) {\n                if (nextConfig.experimental.dynamicIO && !isPrefetchRSCRequest && !context.renderOpts.isPossibleServerAction) {\n                    const warmup = await routeModule.warmup(nextReq, nextRes, context);\n                    // If the warmup is successful, we should use the resume data\n                    // cache from the warmup.\n                    if (warmup.metadata.renderResumeDataCache) {\n                        context.renderOpts.renderResumeDataCache = warmup.metadata.renderResumeDataCache;\n                    }\n                }\n            }\n            return routeModule.render(nextReq, nextRes, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const doRender = async ({ span, postponed, fallbackRouteParams })=>{\n            const context = {\n                query,\n                params,\n                page: normalizedSrcPage,\n                sharedContext: {\n                    buildId\n                },\n                serverComponentsHmrCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'serverComponentsHmrCache'),\n                fallbackRouteParams,\n                renderOpts: {\n                    App: ()=>null,\n                    Document: ()=>null,\n                    pageConfig: {},\n                    ComponentMod,\n                    Component: (0,next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__.interopDefault)(ComponentMod),\n                    params,\n                    routeModule,\n                    page: srcPage,\n                    postponed,\n                    shouldWaitOnAllReady,\n                    serveStreamingMetadata,\n                    supportsDynamicResponse: typeof postponed === 'string' || supportsDynamicResponse,\n                    buildManifest,\n                    nextFontManifest,\n                    reactLoadableManifest,\n                    subresourceIntegrityManifest,\n                    serverActionsManifest,\n                    clientReferenceManifest,\n                    setIsrStatus: routerServerContext == null ? void 0 : routerServerContext.setIsrStatus,\n                    dir: routeModule.projectDir,\n                    isDraftMode,\n                    isRevalidate: isSSG && !postponed && !isDynamicRSCRequest,\n                    botType,\n                    isOnDemandRevalidate,\n                    isPossibleServerAction,\n                    assetPrefix: nextConfig.assetPrefix,\n                    nextConfigOutput: nextConfig.output,\n                    crossOrigin: nextConfig.crossOrigin,\n                    trailingSlash: nextConfig.trailingSlash,\n                    previewProps: prerenderManifest.preview,\n                    deploymentId: nextConfig.deploymentId,\n                    enableTainting: nextConfig.experimental.taint,\n                    htmlLimitedBots: nextConfig.htmlLimitedBots,\n                    devtoolSegmentExplorer: nextConfig.experimental.devtoolSegmentExplorer,\n                    reactMaxHeadersLength: nextConfig.reactMaxHeadersLength,\n                    multiZoneDraftMode,\n                    incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'incrementalCache'),\n                    cacheLifeProfiles: nextConfig.experimental.cacheLife,\n                    basePath: nextConfig.basePath,\n                    serverActions: nextConfig.experimental.serverActions,\n                    ...isDebugStaticShell || isDebugDynamicAccesses ? {\n                        nextExport: true,\n                        supportsDynamicResponse: false,\n                        isStaticGeneration: true,\n                        isRevalidate: true,\n                        isDebugDynamicAccesses: isDebugDynamicAccesses\n                    } : {},\n                    experimental: {\n                        isRoutePPREnabled,\n                        expireTime: nextConfig.expireTime,\n                        staleTimes: nextConfig.experimental.staleTimes,\n                        dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                        clientSegmentCache: Boolean(nextConfig.experimental.clientSegmentCache),\n                        dynamicOnHover: Boolean(nextConfig.experimental.dynamicOnHover),\n                        inlineCss: Boolean(nextConfig.experimental.inlineCss),\n                        authInterrupts: Boolean(nextConfig.experimental.authInterrupts),\n                        clientTraceMetadata: nextConfig.experimental.clientTraceMetadata || []\n                    },\n                    waitUntil: ctx.waitUntil,\n                    onClose: (cb)=>{\n                        res.on('close', cb);\n                    },\n                    onAfterTaskError: ()=>{},\n                    onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext),\n                    err: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'invokeError'),\n                    dev: routeModule.isDev\n                }\n            };\n            const result = await invokeRouteModule(span, context);\n            const { metadata } = result;\n            const { cacheControl, headers = {}, // Add any fetch tags that were on the page to the response headers.\n            fetchTags: cacheTags } = metadata;\n            if (cacheTags) {\n                headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n            }\n            // Pull any fetch metrics from the render onto the request.\n            ;\n            req.fetchMetrics = metadata.fetchMetrics;\n            // we don't throw static to dynamic errors in dev as isSSG\n            // is a best guess in dev since we don't have the prerender pass\n            // to know whether the path is actually static or not\n            if (isSSG && (cacheControl == null ? void 0 : cacheControl.revalidate) === 0 && !routeModule.isDev && !isRoutePPREnabled) {\n                const staticBailoutInfo = metadata.staticBailoutInfo;\n                const err = Object.defineProperty(new Error(`Page changed from static to dynamic at runtime ${resolvedPathname}${(staticBailoutInfo == null ? void 0 : staticBailoutInfo.description) ? `, reason: ${staticBailoutInfo.description}` : ``}` + `\\nsee more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E132\",\n                    enumerable: false,\n                    configurable: true\n                });\n                if (staticBailoutInfo == null ? void 0 : staticBailoutInfo.stack) {\n                    const stack = staticBailoutInfo.stack;\n                    err.stack = err.message + stack.substring(stack.indexOf('\\n'));\n                }\n                throw err;\n            }\n            return {\n                value: {\n                    kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE,\n                    html: result,\n                    headers,\n                    rscData: metadata.flightData,\n                    postponed: metadata.postponed,\n                    status: metadata.statusCode,\n                    segmentData: metadata.segmentData\n                },\n                cacheControl\n            };\n        };\n        const responseGenerator = async ({ hasResolved, previousCacheEntry, isRevalidating, span })=>{\n            const isProduction = routeModule.isDev === false;\n            const didRespond = hasResolved || res.writableEnded;\n            // skip on-demand revalidate if cache is not present and\n            // revalidate-if-generated is set\n            if (isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry && !minimalMode) {\n                if (routerServerContext == null ? void 0 : routerServerContext.render404) {\n                    await routerServerContext.render404(req, res);\n                } else {\n                    res.statusCode = 404;\n                    res.end('This page could not be found');\n                }\n                return null;\n            }\n            let fallbackMode;\n            if (prerenderInfo) {\n                fallbackMode = (0,next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.parseFallbackField)(prerenderInfo.fallback);\n            }\n            // When serving a bot request, we want to serve a blocking render and not\n            // the prerendered page. This ensures that the correct content is served\n            // to the bot in the head.\n            if (fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.PRERENDER && (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.isBot)(userAgent)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if ((previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) === -1) {\n                isOnDemandRevalidate = true;\n            }\n            // TODO: adapt for PPR\n            // only allow on-demand revalidate for fallback: true/blocking\n            // or for prerendered fallback: false paths\n            if (isOnDemandRevalidate && (fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND || previousCacheEntry)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if (!minimalMode && fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER && staticPathKey && !didRespond && !isDraftMode && pageIsDynamic && (isProduction || !isPrerendered)) {\n                // if the page has dynamicParams: false and this pathname wasn't\n                // prerendered trigger the no fallback handling\n                if (// In development, fall through to render to handle missing\n                // getStaticPaths.\n                (isProduction || prerenderInfo) && // When fallback isn't present, abort this render so we 404\n                fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND) {\n                    throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__.NoFallbackError();\n                }\n                let fallbackResponse;\n                if (isRoutePPREnabled && !isRSCRequest) {\n                    // We use the response cache here to handle the revalidation and\n                    // management of the fallback shell.\n                    fallbackResponse = await routeModule.handleResponse({\n                        cacheKey: isProduction ? normalizedSrcPage : null,\n                        req,\n                        nextConfig,\n                        routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                        isFallback: true,\n                        prerenderManifest,\n                        isRoutePPREnabled,\n                        responseGenerator: async ()=>doRender({\n                                span,\n                                // We pass `undefined` as rendering a fallback isn't resumed\n                                // here.\n                                postponed: undefined,\n                                fallbackRouteParams: // If we're in production or we're debugging the fallback\n                                // shell then we should postpone when dynamic params are\n                                // accessed.\n                                isProduction || isDebugFallbackShell ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(normalizedSrcPage) : null\n                            }),\n                        waitUntil: ctx.waitUntil\n                    });\n                    // If the fallback response was set to null, then we should return null.\n                    if (fallbackResponse === null) return null;\n                    // Otherwise, if we did get a fallback response, we should return it.\n                    if (fallbackResponse) {\n                        // Remove the cache control from the response to prevent it from being\n                        // used in the surrounding cache.\n                        delete fallbackResponse.cacheControl;\n                        return fallbackResponse;\n                    }\n                }\n            }\n            // Only requests that aren't revalidating can be resumed. If we have the\n            // minimal postponed data, then we should resume the render with it.\n            const postponed = !isOnDemandRevalidate && !isRevalidating && minimalPostponed ? minimalPostponed : undefined;\n            // When we're in minimal mode, if we're trying to debug the static shell,\n            // we should just return nothing instead of resuming the dynamic render.\n            if ((isDebugStaticShell || isDebugDynamicAccesses) && typeof postponed !== 'undefined') {\n                return {\n                    cacheControl: {\n                        revalidate: 1,\n                        expire: undefined\n                    },\n                    value: {\n                        kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.PAGES,\n                        html: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                        pageData: {},\n                        headers: undefined,\n                        status: undefined\n                    }\n                };\n            }\n            // If this is a dynamic route with PPR enabled and the default route\n            // matches were set, then we should pass the fallback route params to\n            // the renderer as this is a fallback revalidation request.\n            const fallbackRouteParams = pageIsDynamic && isRoutePPREnabled && ((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'renderFallbackShell') || isDebugFallbackShell) ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(pathname) : null;\n            // Perform the render.\n            return doRender({\n                span,\n                postponed,\n                fallbackRouteParams\n            });\n        };\n        const handleResponse = async (span)=>{\n            var _cacheEntry_value, _cachedData_headers;\n            const cacheEntry = await routeModule.handleResponse({\n                cacheKey: ssgCacheKey,\n                responseGenerator: (c)=>responseGenerator({\n                        span,\n                        ...c\n                    }),\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                isOnDemandRevalidate,\n                isRoutePPREnabled,\n                req,\n                nextConfig,\n                prerenderManifest,\n                waitUntil: ctx.waitUntil\n            });\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            // In dev, we should not cache pages for any reason.\n            if (routeModule.isDev) {\n                res.setHeader('Cache-Control', 'no-store, must-revalidate');\n            }\n            if (!cacheEntry) {\n                if (ssgCacheKey) {\n                    // A cache entry might not be generated if a response is written\n                    // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n                    // have a cache key. If we do have a cache key but we don't end up\n                    // with a cache entry, then either Next.js or the application has a\n                    // bug that needs fixing.\n                    throw Object.defineProperty(new Error('invariant: cache entry required but not generated'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E62\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                return null;\n            }\n            if (((_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant app-page handler received invalid cache entry ${(_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E707\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            const didPostpone = typeof cacheEntry.value.postponed === 'string';\n            if (isSSG && // We don't want to send a cache header for requests that contain dynamic\n            // data. If this is a Dynamic RSC request or wasn't a Prefetch RSC\n            // request, then we should set the cache header.\n            !isDynamicRSCRequest && (!didPostpone || isPrefetchRSCRequest)) {\n                if (!minimalMode) {\n                    // set x-nextjs-cache header to match the header\n                    // we set for the image-optimizer\n                    res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n                }\n                // Set a header used by the client router to signal the response is static\n                // and should respect the `static` cache staleTime value.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_IS_PRERENDER_HEADER, '1');\n            }\n            const { value: cachedData } = cacheEntry;\n            // Coerce the cache control parameter from the render.\n            let cacheControl;\n            // If this is a resume request in minimal mode it is streamed with dynamic\n            // content and should not be cached.\n            if (minimalPostponed) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (minimalMode && isRSCRequest && !isPrefetchRSCRequest && isRoutePPREnabled) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (!routeModule.isDev) {\n                // If this is a preview mode request, we shouldn't cache it\n                if (isDraftMode) {\n                    cacheControl = {\n                        revalidate: 0,\n                        expire: undefined\n                    };\n                } else if (!isSSG) {\n                    if (!res.getHeader('Cache-Control')) {\n                        cacheControl = {\n                            revalidate: 0,\n                            expire: undefined\n                        };\n                    }\n                } else if (cacheEntry.cacheControl) {\n                    // If the cache entry has a cache control with a revalidate value that's\n                    // a number, use it.\n                    if (typeof cacheEntry.cacheControl.revalidate === 'number') {\n                        var _cacheEntry_cacheControl;\n                        if (cacheEntry.cacheControl.revalidate < 1) {\n                            throw Object.defineProperty(new Error(`Invalid revalidate configuration provided: ${cacheEntry.cacheControl.revalidate} < 1`), \"__NEXT_ERROR_CODE\", {\n                                value: \"E22\",\n                                enumerable: false,\n                                configurable: true\n                            });\n                        }\n                        cacheControl = {\n                            revalidate: cacheEntry.cacheControl.revalidate,\n                            expire: ((_cacheEntry_cacheControl = cacheEntry.cacheControl) == null ? void 0 : _cacheEntry_cacheControl.expire) ?? nextConfig.expireTime\n                        };\n                    } else {\n                        cacheControl = {\n                            revalidate: next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.CACHE_ONE_YEAR,\n                            expire: undefined\n                        };\n                    }\n                }\n            }\n            cacheEntry.cacheControl = cacheControl;\n            if (typeof segmentPrefetchHeader === 'string' && (cachedData == null ? void 0 : cachedData.kind) === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE && cachedData.segmentData) {\n                var _cachedData_headers1;\n                // This is a prefetch request issued by the client Segment Cache. These\n                // should never reach the application layer (lambda). We should either\n                // respond from the cache (HIT) or respond with 204 No Content (MISS).\n                // Set a header to indicate that PPR is enabled for this route. This\n                // lets the client distinguish between a regular cache miss and a cache\n                // miss due to PPR being disabled. In other contexts this header is used\n                // to indicate that the response contains dynamic data, but here we're\n                // only using it to indicate that the feature is enabled — the segment\n                // response itself contains whether the data is dynamic.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '2');\n                // Add the cache tags header to the response if it exists and we're in\n                // minimal mode while rendering a static page.\n                const tags = (_cachedData_headers1 = cachedData.headers) == null ? void 0 : _cachedData_headers1[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                    res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n                }\n                const matchedSegment = cachedData.segmentData.get(segmentPrefetchHeader);\n                if (matchedSegment !== undefined) {\n                    // Cache hit\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(matchedSegment),\n                        cacheControl: cacheEntry.cacheControl\n                    });\n                }\n                // Cache miss. Either a cache entry for this route has not been generated\n                // (which technically should not be possible when PPR is enabled, because\n                // at a minimum there should always be a fallback entry) or there's no\n                // match for the requested segment. Respond with a 204 No Content. We\n                // don't bother to respond with 404, because these requests are only\n                // issued as part of a prefetch.\n                res.statusCode = 204;\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If there's a callback for `onCacheEntry`, call it with the cache entry\n            // and the revalidate options.\n            const onCacheEntry = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'onCacheEntry');\n            if (onCacheEntry) {\n                const finished = await onCacheEntry({\n                    ...cacheEntry,\n                    // TODO: remove this when upstream doesn't\n                    // always expect this value to be \"PAGE\"\n                    value: {\n                        ...cacheEntry.value,\n                        kind: 'PAGE'\n                    }\n                }, {\n                    url: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'initURL')\n                });\n                if (finished) {\n                    // TODO: maybe we have to end the request?\n                    return null;\n                }\n            }\n            // If the request has a postponed state and it's a resume request we\n            // should error.\n            if (didPostpone && minimalPostponed) {\n                throw Object.defineProperty(new Error('Invariant: postponed state should not be present on a resume request'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E396\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (cachedData.headers) {\n                const headers = {\n                    ...cachedData.headers\n                };\n                if (!minimalMode || !isSSG) {\n                    delete headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                }\n                for (let [key, value] of Object.entries(headers)){\n                    if (typeof value === 'undefined') continue;\n                    if (Array.isArray(value)) {\n                        for (const v of value){\n                            res.appendHeader(key, v);\n                        }\n                    } else if (typeof value === 'number') {\n                        value = value.toString();\n                        res.appendHeader(key, value);\n                    } else {\n                        res.appendHeader(key, value);\n                    }\n                }\n            }\n            // Add the cache tags header to the response if it exists and we're in\n            // minimal mode while rendering a static page.\n            const tags = (_cachedData_headers = cachedData.headers) == null ? void 0 : _cachedData_headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n            if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n            }\n            // If the request is a data request, then we shouldn't set the status code\n            // from the response because it should always be 200. This should be gated\n            // behind the experimental PPR flag.\n            if (cachedData.status && (!isRSCRequest || !isRoutePPREnabled)) {\n                res.statusCode = cachedData.status;\n            }\n            // Redirect information is encoded in RSC payload, so we don't need to use redirect status codes\n            if (!minimalMode && cachedData.status && next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__.RedirectStatusCode[cachedData.status] && isRSCRequest) {\n                res.statusCode = 200;\n            }\n            // Mark that the request did postpone.\n            if (didPostpone) {\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '1');\n            }\n            // we don't go through this block when preview mode is true\n            // as preview mode is a dynamic request (bypasses cache) and doesn't\n            // generate both HTML and payloads in the same request so continue to just\n            // return the generated payload\n            if (isRSCRequest && !isDraftMode) {\n                // If this is a dynamic RSC request, then stream the response.\n                if (typeof cachedData.rscData === 'undefined') {\n                    if (cachedData.postponed) {\n                        throw Object.defineProperty(new Error('Invariant: Expected postponed to be undefined'), \"__NEXT_ERROR_CODE\", {\n                            value: \"E372\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    }\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: cachedData.html,\n                        // Dynamic RSC responses cannot be cached, even if they're\n                        // configured with `force-static` because we have no way of\n                        // distinguishing between `force-static` and pages that have no\n                        // postponed state.\n                        // TODO: distinguish `force-static` from pages with no postponed state (static)\n                        cacheControl: isDynamicRSCRequest ? {\n                            revalidate: 0,\n                            expire: undefined\n                        } : cacheEntry.cacheControl\n                    });\n                }\n                // As this isn't a prefetch request, we should serve the static flight\n                // data.\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(cachedData.rscData),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // This is a request for HTML data.\n            let body = cachedData.html;\n            // If there's no postponed state, we should just serve the HTML. This\n            // should also be the case for a resume request because it's completed\n            // as a server render (rather than a static render).\n            if (!didPostpone || minimalMode) {\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If we're debugging the static shell or the dynamic API accesses, we\n            // should just serve the HTML without resuming the render. The returned\n            // HTML will be the static shell so all the Dynamic API's will be used\n            // during static generation.\n            if (isDebugStaticShell || isDebugDynamicAccesses) {\n                // Since we're not resuming the render, we need to at least add the\n                // closing body and html tags to create valid HTML.\n                body.chain(new ReadableStream({\n                    start (controller) {\n                        controller.enqueue(next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__.ENCODED_TAGS.CLOSED.BODY_AND_HTML);\n                        controller.close();\n                    }\n                }));\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: {\n                        revalidate: 0,\n                        expire: undefined\n                    }\n                });\n            }\n            // This request has postponed, so let's create a new transformer that the\n            // dynamic data can pipe to that will attach the dynamic data to the end\n            // of the response.\n            const transformer = new TransformStream();\n            body.chain(transformer.readable);\n            // Perform the render again, but this time, provide the postponed state.\n            // We don't await because we want the result to start streaming now, and\n            // we've already chained the transformer's readable to the render result.\n            doRender({\n                span,\n                postponed: cachedData.postponed,\n                // This is a resume render, not a fallback render, so we don't need to\n                // set this.\n                fallbackRouteParams: null\n            }).then(async (result)=>{\n                var _result_value;\n                if (!result) {\n                    throw Object.defineProperty(new Error('Invariant: expected a result to be returned'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E463\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                if (((_result_value = result.value) == null ? void 0 : _result_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                    var _result_value1;\n                    throw Object.defineProperty(new Error(`Invariant: expected a page response, got ${(_result_value1 = result.value) == null ? void 0 : _result_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                        value: \"E305\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                // Pipe the resume result to the transformer.\n                await result.value.html.pipeTo(transformer.writable);\n            }).catch((err)=>{\n                // An error occurred during piping or preparing the render, abort\n                // the transformers writer so we can terminate the stream.\n                transformer.writable.abort(err).catch((e)=>{\n                    console.error(\"couldn't abort transformer\", e);\n                });\n            });\n            return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                req,\n                res,\n                type: 'html',\n                generateEtags: nextConfig.generateEtags,\n                poweredByHeader: nextConfig.poweredByHeader,\n                result: body,\n                // We don't want to cache the response if it has postponed data because\n                // the response being sent to the client it's dynamic parts are streamed\n                // to the client on the same request.\n                cacheControl: {\n                    revalidate: 0,\n                    expire: undefined\n                }\n            });\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            return await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: srcPage,\n                routeType: 'render',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__.getRevalidateReason)({\n                    isRevalidate: isSSG,\n                    isOnDemandRevalidate\n                })\n            }, routerServerContext);\n        }\n        // rethrow so that we can handle serving error page\n        throw err;\n    }\n}\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpatient%2Fdashboard%2Fpage&page=%2Fpatient%2Fdashboard%2Fpage&appPaths=%2Fpatient%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fpatient%2Fdashboard%2Fpage.tsx&appDir=D%3A%5CPhoenixCare%5Cphoenixcare-copilot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CPhoenixCare%5Cphoenixcare-copilot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(rsc)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Csrc%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Csrc%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/auth-context.tsx */ \"(rsc)/./src/contexts/auth-context.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQaG9lbml4Q2FyZSU1QyU1Q3Bob2VuaXhjYXJlLWNvcGlsb3QlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDUGhvZW5peENhcmUlNUMlNUNwaG9lbml4Y2FyZS1jb3BpbG90JTVDJTVDc3JjJTVDJTVDY29udGV4dHMlNUMlNUNhdXRoLWNvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwS0FBNEkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkF1dGhQcm92aWRlclwiXSAqLyBcIkQ6XFxcXFBob2VuaXhDYXJlXFxcXHBob2VuaXhjYXJlLWNvcGlsb3RcXFxcc3JjXFxcXGNvbnRleHRzXFxcXGF1dGgtY29udGV4dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Csrc%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Csrc%5C%5Capp%5C%5Cpatient%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Csrc%5C%5Capp%5C%5Cpatient%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/patient/dashboard/page.tsx */ \"(rsc)/./src/app/patient/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQaG9lbml4Q2FyZSU1QyU1Q3Bob2VuaXhjYXJlLWNvcGlsb3QlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYXRpZW50JTVDJTVDZGFzaGJvYXJkJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9MQUFpSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcUGhvZW5peENhcmVcXFxccGhvZW5peGNhcmUtY29waWxvdFxcXFxzcmNcXFxcYXBwXFxcXHBhdGllbnRcXFxcZGFzaGJvYXJkXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Csrc%5C%5Capp%5C%5Cpatient%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxQaG9lbml4Q2FyZVxccGhvZW5peGNhcmUtY29waWxvdFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8f447f1ca828\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcUGhvZW5peENhcmVcXHBob2VuaXhjYXJlLWNvcGlsb3RcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjhmNDQ3ZjFjYTgyOFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/auth-context */ \"(rsc)/./src/contexts/auth-context.tsx\");\n\n\n\nconst metadata = {\n    title: \"PhoenixCare - AI-Powered Post-Discharge Care\",\n    description: \"AI-powered co-pilot for post-discharge patient care, reducing 30-day readmissions through voice-enabled patient monitoring and support.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"antialiased\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUN1QjtBQUNnQztBQUVoRCxNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFVO3NCQUNkLDRFQUFDVCxnRUFBWUE7MEJBQ1ZLOzs7Ozs7Ozs7Ozs7Ozs7O0FBS1giLCJzb3VyY2VzIjpbIkQ6XFxQaG9lbml4Q2FyZVxccGhvZW5peGNhcmUtY29waWxvdFxcc3JjXFxhcHBcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gXCJuZXh0XCI7XG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XG5pbXBvcnQgeyBBdXRoUHJvdmlkZXIgfSBmcm9tIFwiQC9jb250ZXh0cy9hdXRoLWNvbnRleHRcIjtcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6IFwiUGhvZW5peENhcmUgLSBBSS1Qb3dlcmVkIFBvc3QtRGlzY2hhcmdlIENhcmVcIixcbiAgZGVzY3JpcHRpb246IFwiQUktcG93ZXJlZCBjby1waWxvdCBmb3IgcG9zdC1kaXNjaGFyZ2UgcGF0aWVudCBjYXJlLCByZWR1Y2luZyAzMC1kYXkgcmVhZG1pc3Npb25zIHRocm91Z2ggdm9pY2UtZW5hYmxlZCBwYXRpZW50IG1vbml0b3JpbmcgYW5kIHN1cHBvcnQuXCIsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiBSZWFkb25seTx7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59Pikge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwiYW50aWFsaWFzZWRcIj5cbiAgICAgICAgPEF1dGhQcm92aWRlcj5cbiAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIDwvQXV0aFByb3ZpZGVyPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJBdXRoUHJvdmlkZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/patient/dashboard/page.tsx":
/*!********************************************!*\
  !*** ./src/app/patient/dashboard/page.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\PhoenixCare\\phoenixcare-copilot\\src\\app\\patient\\dashboard\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/contexts/auth-context.tsx":
/*!***************************************!*\
  !*** ./src/contexts/auth-context.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\PhoenixCare\\phoenixcare-copilot\\src\\contexts\\auth-context.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\PhoenixCare\\phoenixcare-copilot\\src\\contexts\\auth-context.tsx",
"useAuth",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(ssr)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(ssr)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Csrc%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Csrc%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/auth-context.tsx */ \"(ssr)/./src/contexts/auth-context.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQaG9lbml4Q2FyZSU1QyU1Q3Bob2VuaXhjYXJlLWNvcGlsb3QlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDUGhvZW5peENhcmUlNUMlNUNwaG9lbml4Y2FyZS1jb3BpbG90JTVDJTVDc3JjJTVDJTVDY29udGV4dHMlNUMlNUNhdXRoLWNvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwS0FBNEkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkF1dGhQcm92aWRlclwiXSAqLyBcIkQ6XFxcXFBob2VuaXhDYXJlXFxcXHBob2VuaXhjYXJlLWNvcGlsb3RcXFxcc3JjXFxcXGNvbnRleHRzXFxcXGF1dGgtY29udGV4dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Csrc%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Csrc%5C%5Capp%5C%5Cpatient%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Csrc%5C%5Capp%5C%5Cpatient%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/patient/dashboard/page.tsx */ \"(ssr)/./src/app/patient/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQaG9lbml4Q2FyZSU1QyU1Q3Bob2VuaXhjYXJlLWNvcGlsb3QlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYXRpZW50JTVDJTVDZGFzaGJvYXJkJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9MQUFpSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcUGhvZW5peENhcmVcXFxccGhvZW5peGNhcmUtY29waWxvdFxcXFxzcmNcXFxcYXBwXFxcXHBhdGllbnRcXFxcZGFzaGJvYXJkXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPhoenixCare%5C%5Cphoenixcare-copilot%5C%5Csrc%5C%5Capp%5C%5Cpatient%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/patient/dashboard/page.tsx":
/*!********************************************!*\
  !*** ./src/app/patient/dashboard/page.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PatientDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/auth-context */ \"(ssr)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _components_auth_protected_route__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/auth/protected-route */ \"(ssr)/./src/components/auth/protected-route.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_voice_voice_call_interface__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/voice/voice-call-interface */ \"(ssr)/./src/components/voice/voice-call-interface.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Heart_MessageCircle_Phone_Pill_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Heart,MessageCircle,Phone,Pill!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Heart_MessageCircle_Phone_Pill_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Heart,MessageCircle,Phone,Pill!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Heart_MessageCircle_Phone_Pill_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Heart,MessageCircle,Phone,Pill!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Heart_MessageCircle_Phone_Pill_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Heart,MessageCircle,Phone,Pill!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Heart_MessageCircle_Phone_Pill_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Heart,MessageCircle,Phone,Pill!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pill.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Heart_MessageCircle_Phone_Pill_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Heart,MessageCircle,Phone,Pill!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Heart_MessageCircle_Phone_Pill_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Heart,MessageCircle,Phone,Pill!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Heart_MessageCircle_Phone_Pill_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Heart,MessageCircle,Phone,Pill!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction PatientDashboard() {\n    const { user } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [showVoiceCall, setShowVoiceCall] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const patientData = user?.patientData;\n    const upcomingAppointments = [\n        {\n            id: '1',\n            type: 'Follow-up',\n            doctor: 'Dr. Sarah Johnson',\n            date: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),\n            time: '10:00 AM'\n        },\n        {\n            id: '2',\n            type: 'Lab Results Review',\n            doctor: 'Dr. Michael Chen',\n            date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),\n            time: '2:30 PM'\n        }\n    ];\n    const recentSymptoms = [\n        {\n            symptom: 'Mild fatigue',\n            severity: 3,\n            date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)\n        },\n        {\n            symptom: 'Slight shortness of breath',\n            severity: 2,\n            date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)\n        }\n    ];\n    const medicationReminders = [\n        {\n            name: 'Metformin',\n            time: '8:00 AM',\n            taken: true\n        },\n        {\n            name: 'Lisinopril',\n            time: '8:00 AM',\n            taken: true\n        },\n        {\n            name: 'Metformin',\n            time: '6:00 PM',\n            taken: false\n        }\n    ];\n    if (showVoiceCall) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_protected_route__WEBPACK_IMPORTED_MODULE_3__.ProtectedRoute, {\n            requiredRoles: [\n                'patient'\n            ],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-background p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowVoiceCall(false),\n                                    className: \"mb-4\",\n                                    children: \"← Back to Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: \"AI Health Assistant\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: \"Connect with your AI health assistant for check-ins and support\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_voice_voice_call_interface__WEBPACK_IMPORTED_MODULE_7__.VoiceCallInterface, {\n                            patientId: user?.id || '',\n                            patientName: user?.name || 'Patient',\n                            onCallEnd: ()=>setShowVoiceCall(false)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                lineNumber: 58,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_protected_route__WEBPACK_IMPORTED_MODULE_3__.ProtectedRoute, {\n        requiredRoles: [\n            'patient'\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"border-b bg-card\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-4 py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: [\n                                                \"Welcome back, \",\n                                                user?.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Your health dashboard and AI assistant\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: ()=>setShowVoiceCall(true),\n                                    className: \"bg-primary hover:bg-primary/90\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Heart_MessageCircle_Phone_Pill_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Talk to AI Assistant\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"container mx-auto px-4 py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Heart_MessageCircle_Phone_Pill_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-5 h-5 text-red-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 116,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Health Status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 117,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                    children: \"Your current health overview\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Overall Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 124,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                variant: \"secondary\",\n                                                                className: \"bg-green-100 text-green-800\",\n                                                                children: \"Stable\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 125,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Last Check-in\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 130,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"2 days ago\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 131,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Next AI Call\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 134,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Tomorrow\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 135,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    children: \"Quick Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                    children: \"Common tasks and features\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"w-full justify-start\",\n                                                    onClick: ()=>setShowVoiceCall(true),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Heart_MessageCircle_Phone_Pill_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Start Voice Check-in\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"w-full justify-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Heart_MessageCircle_Phone_Pill_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 157,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"View Appointments\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"w-full justify-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Heart_MessageCircle_Phone_Pill_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Medication Tracker\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Heart_MessageCircle_Phone_Pill_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"w-5 h-5 text-yellow-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Recent Symptoms\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 172,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                    children: \"Symptoms reported in the last week\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    recentSymptoms.map((symptom, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium\",\n                                                                            children: symptom.symptom\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 181,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-muted-foreground\",\n                                                                            children: symptom.date.toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 182,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 180,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                    variant: symptom.severity <= 2 ? \"secondary\" : symptom.severity <= 5 ? \"default\" : \"destructive\",\n                                                                    children: [\n                                                                        symptom.severity,\n                                                                        \"/10\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 186,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 21\n                                                        }, this)),\n                                                    recentSymptoms.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: \"No symptoms reported\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Heart_MessageCircle_Phone_Pill_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-5 h-5 text-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Upcoming Appointments\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                children: \"Your scheduled medical appointments\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: upcomingAppointments.map((appointment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-4 border rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: appointment.type\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 216,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: [\n                                                                        \"with \",\n                                                                        appointment.doctor\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 217,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: appointment.date.toLocaleDateString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 220,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: appointment.time\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 223,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, appointment.id, true, {\n                                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Heart_MessageCircle_Phone_Pill_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-5 h-5 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Today's Medications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                children: \"Your medication schedule for today\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: medicationReminders.map((med, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                med.taken ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Heart_MessageCircle_Phone_Pill_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"w-5 h-5 text-green-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 248,\n                                                                    columnNumber: 27\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Heart_MessageCircle_Phone_Pill_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"w-5 h-5 text-yellow-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 250,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: med.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 253,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: med.time\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 254,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 252,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                            variant: med.taken ? \"secondary\" : \"default\",\n                                                            children: med.taken ? 'Taken' : 'Pending'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n            lineNumber: 87,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\app\\\\patient\\\\dashboard\\\\page.tsx\",\n        lineNumber: 86,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/patient/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/protected-route.tsx":
/*!*************************************************!*\
  !*** ./src/components/auth/protected-route.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProtectedRoute: () => (/* binding */ ProtectedRoute)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/auth-context */ \"(ssr)/./src/contexts/auth-context.tsx\");\n/* __next_internal_client_entry_do_not_use__ ProtectedRoute auto */ \n\n\n\nfunction ProtectedRoute({ children, requiredRoles = [], requiredPermissions = [], requireAll = false, fallbackPath = '/login' }) {\n    const { user, isAuthenticated, isLoading } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProtectedRoute.useEffect\": ()=>{\n            if (isLoading) return;\n            if (!isAuthenticated || !user) {\n                router.push(fallbackPath);\n                return;\n            }\n            // Check role requirements\n            if (requiredRoles.length > 0) {\n                const hasRequiredRole = requiredRoles.includes(user.role);\n                if (!hasRequiredRole) {\n                    router.push('/unauthorized');\n                    return;\n                }\n            }\n            // Check permission requirements\n            if (requiredPermissions.length > 0) {\n                const userPermissions = getUserPermissions(user);\n                if (requireAll) {\n                    // User must have ALL required permissions\n                    const hasAllPermissions = requiredPermissions.every({\n                        \"ProtectedRoute.useEffect.hasAllPermissions\": (permission)=>userPermissions.includes(permission)\n                    }[\"ProtectedRoute.useEffect.hasAllPermissions\"]);\n                    if (!hasAllPermissions) {\n                        router.push('/unauthorized');\n                        return;\n                    }\n                } else {\n                    // User must have ANY of the required permissions\n                    const hasAnyPermission = requiredPermissions.some({\n                        \"ProtectedRoute.useEffect.hasAnyPermission\": (permission)=>userPermissions.includes(permission)\n                    }[\"ProtectedRoute.useEffect.hasAnyPermission\"]);\n                    if (!hasAnyPermission) {\n                        router.push('/unauthorized');\n                        return;\n                    }\n                }\n            }\n        }\n    }[\"ProtectedRoute.useEffect\"], [\n        isAuthenticated,\n        user,\n        isLoading,\n        router,\n        requiredRoles,\n        requiredPermissions,\n        requireAll,\n        fallbackPath\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n            }, void 0, false, {\n                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\auth\\\\protected-route.tsx\",\n                lineNumber: 72,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\auth\\\\protected-route.tsx\",\n            lineNumber: 71,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isAuthenticated || !user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\nfunction getUserPermissions(user) {\n    // Get permissions based on user role and specific admin permissions\n    const rolePermissions = {\n        patient: [],\n        medical_staff: [\n            'patient_data_access'\n        ],\n        admin: [\n            'user_management',\n            'system_config',\n            'data_analytics',\n            'billing',\n            'audit_logs',\n            'patient_data_access',\n            'staff_management',\n            'ai_configuration'\n        ]\n    };\n    let permissions = rolePermissions[user.role] || [];\n    // Add specific admin permissions if user has adminData\n    if (user.role === 'admin' && user.adminData?.permissions) {\n        permissions = [\n            ...new Set([\n                ...permissions,\n                ...user.adminData.permissions\n            ])\n        ];\n    }\n    return permissions;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/protected-route.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\",\n            success: \"border-transparent bg-green-500 text-white hover:bg-green-600\",\n            warning: \"border-transparent bg-yellow-500 text-white hover:bg-yellow-600\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 45,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 23,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 35,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 50,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 62,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 70,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9jYXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBOEI7QUFDRTtBQUVoQyxNQUFNRSxxQkFBT0YsNkNBQWdCLENBRzNCLENBQUMsRUFBRUksU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDQztRQUNDRCxLQUFLQTtRQUNMRixXQUFXSCw4Q0FBRUEsQ0FDWCw0REFDQUc7UUFFRCxHQUFHQyxLQUFLOzs7Ozs7QUFHYkgsS0FBS00sV0FBVyxHQUFHO0FBRW5CLE1BQU1DLDJCQUFhVCw2Q0FBZ0IsQ0FHakMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNDO1FBQ0NELEtBQUtBO1FBQ0xGLFdBQVdILDhDQUFFQSxDQUFDLGlDQUFpQ0c7UUFDOUMsR0FBR0MsS0FBSzs7Ozs7O0FBR2JJLFdBQVdELFdBQVcsR0FBRztBQUV6QixNQUFNRSwwQkFBWVYsNkNBQWdCLENBR2hDLENBQUMsRUFBRUksU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDSztRQUNDTCxLQUFLQTtRQUNMRixXQUFXSCw4Q0FBRUEsQ0FDWCxzREFDQUc7UUFFRCxHQUFHQyxLQUFLOzs7Ozs7QUFHYkssVUFBVUYsV0FBVyxHQUFHO0FBRXhCLE1BQU1JLGdDQUFrQlosNkNBQWdCLENBR3RDLENBQUMsRUFBRUksU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDTztRQUNDUCxLQUFLQTtRQUNMRixXQUFXSCw4Q0FBRUEsQ0FBQyxpQ0FBaUNHO1FBQzlDLEdBQUdDLEtBQUs7Ozs7OztBQUdiTyxnQkFBZ0JKLFdBQVcsR0FBRztBQUU5QixNQUFNTSw0QkFBY2QsNkNBQWdCLENBR2xDLENBQUMsRUFBRUksU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDQztRQUFJRCxLQUFLQTtRQUFLRixXQUFXSCw4Q0FBRUEsQ0FBQyxZQUFZRztRQUFhLEdBQUdDLEtBQUs7Ozs7OztBQUVoRVMsWUFBWU4sV0FBVyxHQUFHO0FBRTFCLE1BQU1PLDJCQUFhZiw2Q0FBZ0IsQ0FHakMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNDO1FBQ0NELEtBQUtBO1FBQ0xGLFdBQVdILDhDQUFFQSxDQUFDLDhCQUE4Qkc7UUFDM0MsR0FBR0MsS0FBSzs7Ozs7O0FBR2JVLFdBQVdQLFdBQVcsR0FBRztBQUV1RCIsInNvdXJjZXMiOlsiRDpcXFBob2VuaXhDYXJlXFxwaG9lbml4Y2FyZS1jb3BpbG90XFxzcmNcXGNvbXBvbmVudHNcXHVpXFxjYXJkLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBDYXJkID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTERpdkVsZW1lbnQsXG4gIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxEaXZFbGVtZW50PlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8ZGl2XG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcbiAgICAgIFwicm91bmRlZC1sZyBib3JkZXIgYmctY2FyZCB0ZXh0LWNhcmQtZm9yZWdyb3VuZCBzaGFkb3ctc21cIixcbiAgICAgIGNsYXNzTmFtZVxuICAgICl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcbkNhcmQuZGlzcGxheU5hbWUgPSBcIkNhcmRcIlxuXG5jb25zdCBDYXJkSGVhZGVyID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTERpdkVsZW1lbnQsXG4gIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxEaXZFbGVtZW50PlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8ZGl2XG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcImZsZXggZmxleC1jb2wgc3BhY2UteS0xLjUgcC02XCIsIGNsYXNzTmFtZSl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcbkNhcmRIZWFkZXIuZGlzcGxheU5hbWUgPSBcIkNhcmRIZWFkZXJcIlxuXG5jb25zdCBDYXJkVGl0bGUgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBIVE1MUGFyYWdyYXBoRWxlbWVudCxcbiAgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTEhlYWRpbmdFbGVtZW50PlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8aDNcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgXCJ0ZXh0LTJ4bCBmb250LXNlbWlib2xkIGxlYWRpbmctbm9uZSB0cmFja2luZy10aWdodFwiLFxuICAgICAgY2xhc3NOYW1lXG4gICAgKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuQ2FyZFRpdGxlLmRpc3BsYXlOYW1lID0gXCJDYXJkVGl0bGVcIlxuXG5jb25zdCBDYXJkRGVzY3JpcHRpb24gPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBIVE1MUGFyYWdyYXBoRWxlbWVudCxcbiAgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTFBhcmFncmFwaEVsZW1lbnQ+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxwXG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCIsIGNsYXNzTmFtZSl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcbkNhcmREZXNjcmlwdGlvbi5kaXNwbGF5TmFtZSA9IFwiQ2FyZERlc2NyaXB0aW9uXCJcblxuY29uc3QgQ2FyZENvbnRlbnQgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBIVE1MRGl2RWxlbWVudCxcbiAgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTERpdkVsZW1lbnQ+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxkaXYgcmVmPXtyZWZ9IGNsYXNzTmFtZT17Y24oXCJwLTYgcHQtMFwiLCBjbGFzc05hbWUpfSB7Li4ucHJvcHN9IC8+XG4pKVxuQ2FyZENvbnRlbnQuZGlzcGxheU5hbWUgPSBcIkNhcmRDb250ZW50XCJcblxuY29uc3QgQ2FyZEZvb3RlciA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIEhUTUxEaXZFbGVtZW50LFxuICBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MRGl2RWxlbWVudD5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPGRpdlxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24oXCJmbGV4IGl0ZW1zLWNlbnRlciBwLTYgcHQtMFwiLCBjbGFzc05hbWUpfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpXG5DYXJkRm9vdGVyLmRpc3BsYXlOYW1lID0gXCJDYXJkRm9vdGVyXCJcblxuZXhwb3J0IHsgQ2FyZCwgQ2FyZEhlYWRlciwgQ2FyZEZvb3RlciwgQ2FyZFRpdGxlLCBDYXJkRGVzY3JpcHRpb24sIENhcmRDb250ZW50IH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiQ2FyZCIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJwcm9wcyIsInJlZiIsImRpdiIsImRpc3BsYXlOYW1lIiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsImgzIiwiQ2FyZERlc2NyaXB0aW9uIiwicCIsIkNhcmRDb250ZW50IiwiQ2FyZEZvb3RlciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/voice/voice-call-interface.tsx":
/*!*******************************************************!*\
  !*** ./src/components/voice/voice-call-interface.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VoiceCallInterface: () => (/* binding */ VoiceCallInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _stores_voice_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/stores/voice-store */ \"(ssr)/./src/stores/voice-store.ts\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_MessageSquare_Mic_MicOff_Phone_PhoneOff_Settings_Users_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,MessageSquare,Mic,MicOff,Phone,PhoneOff,Settings,Users,Volume2,VolumeX!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_MessageSquare_Mic_MicOff_Phone_PhoneOff_Settings_Users_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,MessageSquare,Mic,MicOff,Phone,PhoneOff,Settings,Users,Volume2,VolumeX!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_MessageSquare_Mic_MicOff_Phone_PhoneOff_Settings_Users_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,MessageSquare,Mic,MicOff,Phone,PhoneOff,Settings,Users,Volume2,VolumeX!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_MessageSquare_Mic_MicOff_Phone_PhoneOff_Settings_Users_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,MessageSquare,Mic,MicOff,Phone,PhoneOff,Settings,Users,Volume2,VolumeX!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_MessageSquare_Mic_MicOff_Phone_PhoneOff_Settings_Users_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,MessageSquare,Mic,MicOff,Phone,PhoneOff,Settings,Users,Volume2,VolumeX!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_MessageSquare_Mic_MicOff_Phone_PhoneOff_Settings_Users_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,MessageSquare,Mic,MicOff,Phone,PhoneOff,Settings,Users,Volume2,VolumeX!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/volume-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_MessageSquare_Mic_MicOff_Phone_PhoneOff_Settings_Users_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,MessageSquare,Mic,MicOff,Phone,PhoneOff,Settings,Users,Volume2,VolumeX!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_MessageSquare_Mic_MicOff_Phone_PhoneOff_Settings_Users_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,MessageSquare,Mic,MicOff,Phone,PhoneOff,Settings,Users,Volume2,VolumeX!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_MessageSquare_Mic_MicOff_Phone_PhoneOff_Settings_Users_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,MessageSquare,Mic,MicOff,Phone,PhoneOff,Settings,Users,Volume2,VolumeX!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_MessageSquare_Mic_MicOff_Phone_PhoneOff_Settings_Users_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,MessageSquare,Mic,MicOff,Phone,PhoneOff,Settings,Users,Volume2,VolumeX!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* __next_internal_client_entry_do_not_use__ VoiceCallInterface auto */ \n\n\n\n\n\n\nfunction VoiceCallInterface({ patientId, patientName, onCallEnd }) {\n    const { state, controls } = (0,_stores_voice_store__WEBPACK_IMPORTED_MODULE_2__.useVoiceCallStore)();\n    const [showTranscript, setShowTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [audioLevel, setAudioLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const transcriptRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-scroll transcript to bottom\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VoiceCallInterface.useEffect\": ()=>{\n            if (transcriptRef.current) {\n                transcriptRef.current.scrollTop = transcriptRef.current.scrollHeight;\n            }\n        }\n    }[\"VoiceCallInterface.useEffect\"], [\n        state.transcript\n    ]);\n    // Mock audio level animation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VoiceCallInterface.useEffect\": ()=>{\n            if (state.isConnected && !state.isMuted) {\n                const interval = setInterval({\n                    \"VoiceCallInterface.useEffect.interval\": ()=>{\n                        setAudioLevel(Math.random() * 100);\n                    }\n                }[\"VoiceCallInterface.useEffect.interval\"], 100);\n                return ({\n                    \"VoiceCallInterface.useEffect\": ()=>clearInterval(interval)\n                })[\"VoiceCallInterface.useEffect\"];\n            } else {\n                setAudioLevel(0);\n            }\n        }\n    }[\"VoiceCallInterface.useEffect\"], [\n        state.isConnected,\n        state.isMuted\n    ]);\n    const handleStartCall = async ()=>{\n        try {\n            await controls.startCall(patientId, 'patient_checkin');\n        } catch (error) {\n            console.error('Failed to start call:', error);\n        }\n    };\n    const handleEndCall = async ()=>{\n        try {\n            await controls.endCall();\n            onCallEnd?.();\n        } catch (error) {\n            console.error('Failed to end call:', error);\n        }\n    };\n    const getCallStatusColor = ()=>{\n        if (state.isConnecting) return 'bg-yellow-500';\n        if (state.isConnected) return 'bg-green-500';\n        if (state.error) return 'bg-red-500';\n        return 'bg-gray-500';\n    };\n    const getCallStatusText = ()=>{\n        if (state.isConnecting) return 'Connecting...';\n        if (state.isConnected) return 'Connected';\n        if (state.error) return 'Error';\n        return 'Idle';\n    };\n    const formatDuration = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    };\n    const [callDuration, setCallDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VoiceCallInterface.useEffect\": ()=>{\n            let interval;\n            if (state.isConnected && state.session) {\n                interval = setInterval({\n                    \"VoiceCallInterface.useEffect\": ()=>{\n                        const now = new Date();\n                        const duration = Math.floor((now.getTime() - state.session.startTime.getTime()) / 1000);\n                        setCallDuration(duration);\n                    }\n                }[\"VoiceCallInterface.useEffect\"], 1000);\n            }\n            return ({\n                \"VoiceCallInterface.useEffect\": ()=>clearInterval(interval)\n            })[\"VoiceCallInterface.useEffect\"];\n        }\n    }[\"VoiceCallInterface.useEffect\"], [\n        state.isConnected,\n        state.session\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    className: \"pb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `w-3 h-3 rounded-full ${getCallStatusColor()}`\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"text-lg\",\n                                                children: patientName\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: [\n                                                    getCallStatusText(),\n                                                    state.isConnected && ` • ${formatDuration(callDuration)}`\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    state.staffJoined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_MessageSquare_Mic_MicOff_Phone_PhoneOff_Settings_Users_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Staff Joined\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 17\n                                    }, this),\n                                    state.isRecording && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"destructive\",\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-red-500 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Recording\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this),\n            state.isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"pt-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center space-x-2 h-16\",\n                            children: Array.from({\n                                length: 20\n                            }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `w-2 bg-primary transition-all duration-100 ${audioLevel > i * 5 ? 'opacity-100' : 'opacity-20'}`,\n                                    style: {\n                                        height: `${Math.max(4, audioLevel / 100 * 64)}px`\n                                    }\n                                }, i, false, {\n                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-sm text-muted-foreground mt-2\",\n                            children: state.aiResponding ? 'AI is speaking...' : 'Listening...'\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                lineNumber: 143,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center space-x-4\",\n                        children: !state.isConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: handleStartCall,\n                            disabled: state.isConnecting,\n                            size: \"lg\",\n                            className: \"bg-green-600 hover:bg-green-700 text-white px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_MessageSquare_Mic_MicOff_Phone_PhoneOff_Settings_Users_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-5 h-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 17\n                                }, this),\n                                state.isConnecting ? 'Connecting...' : 'Start Call'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: controls.toggleMute,\n                                    variant: state.isMuted ? \"destructive\" : \"outline\",\n                                    size: \"lg\",\n                                    children: state.isMuted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_MessageSquare_Mic_MicOff_Phone_PhoneOff_Settings_Users_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 36\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_MessageSquare_Mic_MicOff_Phone_PhoneOff_Settings_Users_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 69\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>controls.adjustVolume(state.audioSettings.volume > 0 ? 0 : 0.8),\n                                    variant: \"outline\",\n                                    size: \"lg\",\n                                    children: state.audioSettings.volume > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_MessageSquare_Mic_MicOff_Phone_PhoneOff_Settings_Users_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 21\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_MessageSquare_Mic_MicOff_Phone_PhoneOff_Settings_Users_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: controls.toggleRecording,\n                                    variant: state.isRecording ? \"destructive\" : \"outline\",\n                                    size: \"lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `w-3 h-3 rounded-full ${state.isRecording ? 'bg-white' : 'bg-red-500'}`\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>setShowTranscript(!showTranscript),\n                                    variant: showTranscript ? \"default\" : \"outline\",\n                                    size: \"lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_MessageSquare_Mic_MicOff_Phone_PhoneOff_Settings_Users_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    size: \"lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_MessageSquare_Mic_MicOff_Phone_PhoneOff_Settings_Users_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: handleEndCall,\n                                    variant: \"destructive\",\n                                    size: \"lg\",\n                                    className: \"px-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_MessageSquare_Mic_MicOff_Phone_PhoneOff_Settings_Users_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-5 h-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"End Call\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, this),\n            state.isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"pt-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-muted-foreground\",\n                                                children: \"Quality: \"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `font-medium ${state.connectionQuality.signalStrength === 'excellent' ? 'text-green-600' : state.connectionQuality.signalStrength === 'good' ? 'text-blue-600' : state.connectionQuality.signalStrength === 'fair' ? 'text-yellow-600' : 'text-red-600'}`,\n                                                children: state.connectionQuality.signalStrength\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-muted-foreground\",\n                                                children: \"Latency: \"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: [\n                                                    state.connectionQuality.latency,\n                                                    \"ms\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 15\n                            }, this),\n                            state.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1 text-red-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_MessageSquare_Mic_MicOff_Phone_PhoneOff_Settings_Users_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs\",\n                                        children: state.error\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                lineNumber: 244,\n                columnNumber: 9\n            }, this),\n            showTranscript && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"text-lg\",\n                            children: \"Conversation Transcript\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: transcriptRef,\n                            className: \"h-64 overflow-y-auto bg-gray-50 p-4 rounded-md text-sm font-mono whitespace-pre-wrap\",\n                            children: state.transcript || 'Transcript will appear here during the call...'\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n                lineNumber: 277,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\components\\\\voice\\\\voice-call-interface.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/voice/voice-call-interface.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/auth-context.tsx":
/*!***************************************!*\
  !*** ./src/contexts/auth-context.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _stores_auth_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/stores/auth-store */ \"(ssr)/./src/stores/auth-store.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nfunction AuthProvider({ children }) {\n    const { user, isAuthenticated, isLoading, error, login, register, logout, clearError, hasPermission, hasRole } = (0,_stores_auth_store__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)();\n    // Initialize auth state on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n        // Any initialization logic can go here\n        // For example, checking if stored token is still valid\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const contextValue = {\n        user,\n        isAuthenticated,\n        isLoading,\n        error,\n        login,\n        register,\n        logout,\n        clearError,\n        hasPermission,\n        hasRole\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\PhoenixCare\\\\phoenixcare-copilot\\\\src\\\\contexts\\\\auth-context.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/auth-context.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatTime: () => (/* binding */ formatTime),\n/* harmony export */   getInitials: () => (/* binding */ getInitials)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatDate(date) {\n    return new Intl.DateTimeFormat('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n    }).format(date);\n}\nfunction formatTime(date) {\n    return new Intl.DateTimeFormat('en-US', {\n        hour: '2-digit',\n        minute: '2-digit'\n    }).format(date);\n}\nfunction getInitials(name) {\n    return name.split(' ').map((word)=>word.charAt(0)).join('').toUpperCase().slice(0, 2);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/stores/auth-store.ts":
/*!**********************************!*\
  !*** ./src/stores/auth-store.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var _types_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/types/auth */ \"(ssr)/./src/types/auth.ts\");\n\n\n\n// Mock authentication service - replace with real API calls\nconst mockAuthService = {\n    async login (credentials) {\n        // Simulate API delay\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        // Mock users for testing\n        const mockUsers = {\n            '<EMAIL>': {\n                id: '1',\n                email: '<EMAIL>',\n                name: 'John Patient',\n                role: 'patient',\n                createdAt: new Date(),\n                lastLogin: new Date(),\n                isActive: true,\n                patientData: {\n                    patientId: 'P001',\n                    dateOfBirth: new Date('1980-01-01'),\n                    phoneNumber: '+**********',\n                    emergencyContact: {\n                        name: 'Jane Patient',\n                        phone: '+**********',\n                        relationship: 'Spouse'\n                    },\n                    medicalConditions: [\n                        'Hypertension',\n                        'Diabetes Type 2'\n                    ],\n                    medications: [\n                        'Metformin',\n                        'Lisinopril'\n                    ],\n                    allergies: [\n                        'Penicillin'\n                    ],\n                    preferredLanguage: 'English',\n                    communicationPreferences: {\n                        voiceCalls: true,\n                        textMessages: true,\n                        email: false\n                    }\n                }\n            },\n            '<EMAIL>': {\n                id: '2',\n                email: '<EMAIL>',\n                name: 'Dr. Sarah Staff',\n                role: 'medical_staff',\n                createdAt: new Date(),\n                lastLogin: new Date(),\n                isActive: true,\n                staffData: {\n                    staffId: 'S001',\n                    department: 'Cardiology',\n                    position: 'Physician',\n                    licenseNumber: 'MD123456',\n                    specializations: [\n                        'Cardiology',\n                        'Internal Medicine'\n                    ],\n                    assignedPatients: [\n                        'P001',\n                        'P002',\n                        'P003'\n                    ],\n                    workSchedule: {\n                        monday: {\n                            start: '08:00',\n                            end: '17:00'\n                        },\n                        tuesday: {\n                            start: '08:00',\n                            end: '17:00'\n                        },\n                        wednesday: {\n                            start: '08:00',\n                            end: '17:00'\n                        },\n                        thursday: {\n                            start: '08:00',\n                            end: '17:00'\n                        },\n                        friday: {\n                            start: '08:00',\n                            end: '17:00'\n                        },\n                        saturday: null,\n                        sunday: null\n                    }\n                }\n            },\n            '<EMAIL>': {\n                id: '3',\n                email: '<EMAIL>',\n                name: 'Admin User',\n                role: 'admin',\n                createdAt: new Date(),\n                lastLogin: new Date(),\n                isActive: true,\n                adminData: {\n                    adminId: 'A001',\n                    permissions: [\n                        'user_management',\n                        'system_config',\n                        'data_analytics',\n                        'billing',\n                        'audit_logs',\n                        'patient_data_access',\n                        'staff_management',\n                        'ai_configuration'\n                    ],\n                    managedDepartments: [\n                        'All'\n                    ],\n                    systemAccess: {\n                        userManagement: true,\n                        systemConfiguration: true,\n                        dataAnalytics: true,\n                        billing: true,\n                        auditLogs: true\n                    }\n                }\n            }\n        };\n        const user = mockUsers[credentials.email];\n        if (!user || credentials.password !== 'password') {\n            throw new Error('Invalid email or password');\n        }\n        return {\n            ...user,\n            lastLogin: new Date()\n        };\n    },\n    async register (data) {\n        // Simulate API delay\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        // Mock registration - in real app, this would create user in database\n        const newUser = {\n            id: Math.random().toString(36).substr(2, 9),\n            email: data.email,\n            name: data.name,\n            role: data.role,\n            createdAt: new Date(),\n            isActive: true\n        };\n        // Add role-specific data based on registration\n        if (data.role === 'patient' && data.additionalData) {\n            newUser.patientData = data.additionalData;\n        } else if (data.role === 'medical_staff' && data.additionalData) {\n            newUser.staffData = data.additionalData;\n        } else if (data.role === 'admin' && data.additionalData) {\n            newUser.adminData = data.additionalData;\n        }\n        return newUser;\n    }\n};\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_2__.persist)((set, get)=>({\n        user: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null,\n        login: async (credentials)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const user = await mockAuthService.login(credentials);\n                set({\n                    user,\n                    isAuthenticated: true,\n                    isLoading: false,\n                    error: null\n                });\n            } catch (error) {\n                set({\n                    error: error instanceof Error ? error.message : 'Login failed',\n                    isLoading: false\n                });\n                throw error;\n            }\n        },\n        register: async (data)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const user = await mockAuthService.register(data);\n                set({\n                    user,\n                    isAuthenticated: true,\n                    isLoading: false,\n                    error: null\n                });\n            } catch (error) {\n                set({\n                    error: error instanceof Error ? error.message : 'Registration failed',\n                    isLoading: false\n                });\n                throw error;\n            }\n        },\n        logout: ()=>{\n            set({\n                user: null,\n                isAuthenticated: false,\n                error: null\n            });\n        },\n        clearError: ()=>{\n            set({\n                error: null\n            });\n        },\n        hasPermission: (permission)=>{\n            const { user } = get();\n            if (!user) return false;\n            const rolePermissions = _types_auth__WEBPACK_IMPORTED_MODULE_0__.ROLE_PERMISSIONS[user.role];\n            return rolePermissions.includes(permission);\n        },\n        hasRole: (role)=>{\n            const { user } = get();\n            return user?.role === role;\n        },\n        setUser: (user)=>{\n            set({\n                user,\n                isAuthenticated: true\n            });\n        },\n        setLoading: (isLoading)=>{\n            set({\n                isLoading\n            });\n        },\n        setError: (error)=>{\n            set({\n                error\n            });\n        }\n    }), {\n    name: 'phoenixcare-auth',\n    partialize: (state)=>({\n            user: state.user,\n            isAuthenticated: state.isAuthenticated\n        })\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/stores/auth-store.ts\n");

/***/ }),

/***/ "(ssr)/./src/stores/voice-store.ts":
/*!***********************************!*\
  !*** ./src/stores/voice-store.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useVoiceCallStore: () => (/* binding */ useVoiceCallStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n\nconst initialAudioSettings = {\n    echoCancellation: true,\n    noiseSuppression: true,\n    autoGainControl: true,\n    volume: 0.8,\n    muted: false\n};\nconst initialConnectionQuality = {\n    latency: 0,\n    packetLoss: 0,\n    jitter: 0,\n    bandwidth: 0,\n    signalStrength: 'good'\n};\nconst initialState = {\n    session: null,\n    isConnecting: false,\n    isConnected: false,\n    isMuted: false,\n    isRecording: false,\n    audioDevices: [],\n    audioSettings: initialAudioSettings,\n    connectionQuality: initialConnectionQuality,\n    transcript: '',\n    error: null,\n    staffJoined: false,\n    aiResponding: false\n};\nconst useVoiceCallStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        state: initialState,\n        controls: {\n            startCall: async (patientId, type)=>{\n                const { createSession } = get();\n                set((state)=>({\n                        state: {\n                            ...state.state,\n                            isConnecting: true,\n                            error: null\n                        }\n                    }));\n                try {\n                    const session = createSession(patientId, type);\n                    // Initialize WebRTC connection\n                    await initializeWebRTCConnection();\n                    set((state)=>({\n                            state: {\n                                ...state.state,\n                                session,\n                                isConnecting: false,\n                                isConnected: true\n                            }\n                        }));\n                } catch (error) {\n                    set((state)=>({\n                            state: {\n                                ...state.state,\n                                isConnecting: false,\n                                error: error instanceof Error ? error.message : 'Failed to start call'\n                            }\n                        }));\n                    throw error;\n                }\n            },\n            endCall: async ()=>{\n                const { endSession } = get();\n                try {\n                    // Clean up WebRTC connection\n                    await cleanupWebRTCConnection();\n                    endSession();\n                    set((state)=>({\n                            state: {\n                                ...state.state,\n                                isConnected: false,\n                                isConnecting: false,\n                                session: null,\n                                transcript: '',\n                                staffJoined: false,\n                                aiResponding: false\n                            }\n                        }));\n                } catch (error) {\n                    console.error('Error ending call:', error);\n                }\n            },\n            toggleMute: ()=>{\n                set((state)=>({\n                        state: {\n                            ...state.state,\n                            isMuted: !state.state.isMuted,\n                            audioSettings: {\n                                ...state.state.audioSettings,\n                                muted: !state.state.isMuted\n                            }\n                        }\n                    }));\n            },\n            toggleRecording: ()=>{\n                set((state)=>({\n                        state: {\n                            ...state.state,\n                            isRecording: !state.state.isRecording\n                        }\n                    }));\n            },\n            adjustVolume: (volume)=>{\n                set((state)=>({\n                        state: {\n                            ...state.state,\n                            audioSettings: {\n                                ...state.state.audioSettings,\n                                volume: Math.max(0, Math.min(1, volume))\n                            }\n                        }\n                    }));\n            },\n            switchAudioDevice: async (deviceId, type)=>{\n                const { updateAudioSettings } = get();\n                if (type === 'input') {\n                    updateAudioSettings({\n                        inputDevice: deviceId\n                    });\n                } else {\n                    updateAudioSettings({\n                        outputDevice: deviceId\n                    });\n                }\n                // Apply device change to active stream\n                await applyAudioDeviceChange(deviceId, type);\n            },\n            joinAsStaff: async (staffId)=>{\n                set((state)=>({\n                        state: {\n                            ...state.state,\n                            staffJoined: true\n                        }\n                    }));\n                // Notify other participants\n                await notifyStaffJoined(staffId);\n            },\n            leaveAsStaff: ()=>{\n                set((state)=>({\n                        state: {\n                            ...state.state,\n                            staffJoined: false\n                        }\n                    }));\n            },\n            sendTextMessage: (message)=>{\n                // Implementation for sending text messages during call\n                console.log('Sending text message:', message);\n            },\n            requestEmergencyAssistance: ()=>{\n                // Implementation for emergency assistance\n                console.log('Emergency assistance requested');\n            }\n        },\n        setState: (updates)=>{\n            set((state)=>({\n                    state: {\n                        ...state.state,\n                        ...updates\n                    }\n                }));\n        },\n        resetState: ()=>{\n            set({\n                state: initialState\n            });\n        },\n        initializeAudioDevices: async ()=>{\n            try {\n                const devices = await navigator.mediaDevices.enumerateDevices();\n                const audioDevices = devices.filter((device)=>device.kind === 'audioinput' || device.kind === 'audiooutput').map((device)=>({\n                        deviceId: device.deviceId,\n                        label: device.label || `${device.kind} ${device.deviceId.slice(0, 8)}`,\n                        kind: device.kind\n                    }));\n                set((state)=>({\n                        state: {\n                            ...state.state,\n                            audioDevices\n                        }\n                    }));\n            } catch (error) {\n                console.error('Failed to enumerate audio devices:', error);\n            }\n        },\n        updateAudioSettings: (settings)=>{\n            set((state)=>({\n                    state: {\n                        ...state.state,\n                        audioSettings: {\n                            ...state.state.audioSettings,\n                            ...settings\n                        }\n                    }\n                }));\n        },\n        updateConnectionQuality: (quality)=>{\n            set((state)=>({\n                    state: {\n                        ...state.state,\n                        connectionQuality: {\n                            ...state.state.connectionQuality,\n                            ...quality\n                        }\n                    }\n                }));\n        },\n        handleConnectionError: (error)=>{\n            set((state)=>({\n                    state: {\n                        ...state.state,\n                        error,\n                        isConnected: false,\n                        isConnecting: false\n                    }\n                }));\n        },\n        addTranscriptSegment: (text, speaker)=>{\n            const timestamp = new Date().toLocaleTimeString();\n            const segment = `[${timestamp}] ${speaker.toUpperCase()}: ${text}\\n`;\n            set((state)=>({\n                    state: {\n                        ...state.state,\n                        transcript: state.state.transcript + segment\n                    }\n                }));\n        },\n        clearTranscript: ()=>{\n            set((state)=>({\n                    state: {\n                        ...state.state,\n                        transcript: ''\n                    }\n                }));\n        },\n        createSession: (patientId, type)=>{\n            const session = {\n                id: `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n                patientId,\n                type,\n                status: 'connecting',\n                startTime: new Date(),\n                audioQuality: 'good',\n                metadata: {\n                    patientName: `Patient ${patientId}`,\n                    patientCondition: 'Unknown',\n                    callPurpose: type,\n                    urgencyLevel: 'medium',\n                    connectionAttempts: 1,\n                    remindersSent: 0,\n                    technicalIssues: []\n                }\n            };\n            return session;\n        },\n        updateSession: (updates)=>{\n            set((state)=>({\n                    state: {\n                        ...state.state,\n                        session: state.state.session ? {\n                            ...state.state.session,\n                            ...updates\n                        } : null\n                    }\n                }));\n        },\n        endSession: ()=>{\n            const { state } = get();\n            if (state.session) {\n                const endTime = new Date();\n                const duration = Math.floor((endTime.getTime() - state.session.startTime.getTime()) / 1000);\n                set((currentState)=>({\n                        state: {\n                            ...currentState.state,\n                            session: {\n                                ...state.session,\n                                status: 'disconnecting',\n                                endTime,\n                                duration\n                            }\n                        }\n                    }));\n            }\n        }\n    }));\n// Helper functions for WebRTC operations\nasync function initializeWebRTCConnection() {\n    // Implementation for WebRTC initialization\n    console.log('Initializing WebRTC connection...');\n    // Request microphone permissions\n    try {\n        const stream = await navigator.mediaDevices.getUserMedia({\n            audio: true,\n            video: false\n        });\n        // Store the stream for later use\n        window.currentAudioStream = stream;\n        console.log('Audio stream acquired');\n    } catch (error) {\n        throw new Error('Failed to access microphone');\n    }\n}\nasync function cleanupWebRTCConnection() {\n    // Implementation for WebRTC cleanup\n    console.log('Cleaning up WebRTC connection...');\n    const stream = window.currentAudioStream;\n    if (stream) {\n        stream.getTracks().forEach((track)=>track.stop());\n        window.currentAudioStream = null;\n    }\n}\nasync function applyAudioDeviceChange(deviceId, type) {\n    // Implementation for changing audio devices\n    console.log(`Switching ${type} device to:`, deviceId);\n}\nasync function notifyStaffJoined(staffId) {\n    // Implementation for notifying participants about staff joining\n    console.log('Staff joined call:', staffId);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/stores/voice-store.ts\n");

/***/ }),

/***/ "(ssr)/./src/types/auth.ts":
/*!***************************!*\
  !*** ./src/types/auth.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NAVIGATION_ITEMS: () => (/* binding */ NAVIGATION_ITEMS),\n/* harmony export */   ROLE_PERMISSIONS: () => (/* binding */ ROLE_PERMISSIONS)\n/* harmony export */ });\nconst ROLE_PERMISSIONS = {\n    patient: [],\n    medical_staff: [\n        'patient_data_access'\n    ],\n    admin: [\n        'user_management',\n        'system_config',\n        'data_analytics',\n        'billing',\n        'audit_logs',\n        'patient_data_access',\n        'staff_management',\n        'ai_configuration'\n    ]\n};\nconst NAVIGATION_ITEMS = [\n    // Patient navigation\n    {\n        label: 'My Health',\n        href: '/patient/dashboard',\n        icon: 'Heart',\n        roles: [\n            'patient'\n        ]\n    },\n    {\n        label: 'AI Assistant',\n        href: '/patient/assistant',\n        icon: 'MessageCircle',\n        roles: [\n            'patient'\n        ]\n    },\n    {\n        label: 'Appointments',\n        href: '/patient/appointments',\n        icon: 'Calendar',\n        roles: [\n            'patient'\n        ]\n    },\n    {\n        label: 'Medical History',\n        href: '/patient/history',\n        icon: 'FileText',\n        roles: [\n            'patient'\n        ]\n    },\n    // Medical staff navigation\n    {\n        label: 'Patient Dashboard',\n        href: '/staff/patients',\n        icon: 'Users',\n        roles: [\n            'medical_staff',\n            'admin'\n        ]\n    },\n    {\n        label: 'Voice Calls',\n        href: '/staff/calls',\n        icon: 'Phone',\n        roles: [\n            'medical_staff',\n            'admin'\n        ]\n    },\n    {\n        label: 'AI Conversations',\n        href: '/staff/conversations',\n        icon: 'MessageSquare',\n        roles: [\n            'medical_staff',\n            'admin'\n        ]\n    },\n    {\n        label: 'Reports',\n        href: '/staff/reports',\n        icon: 'BarChart3',\n        roles: [\n            'medical_staff',\n            'admin'\n        ]\n    },\n    // Admin navigation\n    {\n        label: 'User Management',\n        href: '/admin/users',\n        icon: 'UserCog',\n        roles: [\n            'admin'\n        ],\n        permissions: [\n            'user_management'\n        ]\n    },\n    {\n        label: 'System Config',\n        href: '/admin/config',\n        icon: 'Settings',\n        roles: [\n            'admin'\n        ],\n        permissions: [\n            'system_config'\n        ]\n    },\n    {\n        label: 'Analytics',\n        href: '/admin/analytics',\n        icon: 'TrendingUp',\n        roles: [\n            'admin'\n        ],\n        permissions: [\n            'data_analytics'\n        ]\n    },\n    {\n        label: 'Billing',\n        href: '/admin/billing',\n        icon: 'CreditCard',\n        roles: [\n            'admin'\n        ],\n        permissions: [\n            'billing'\n        ]\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/types/auth.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/dynamic-access-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/server/app-render/dynamic-access-async-storage.external.js" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/dynamic-access-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/is-bot":
/*!***********************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/is-bot" ***!
  \***********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/is-bot");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/zustand","vendor-chunks/@radix-ui","vendor-chunks/@swc","vendor-chunks/class-variance-authority","vendor-chunks/clsx","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpatient%2Fdashboard%2Fpage&page=%2Fpatient%2Fdashboard%2Fpage&appPaths=%2Fpatient%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fpatient%2Fdashboard%2Fpage.tsx&appDir=D%3A%5CPhoenixCare%5Cphoenixcare-copilot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CPhoenixCare%5Cphoenixcare-copilot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();