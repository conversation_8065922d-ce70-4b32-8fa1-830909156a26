import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import { AuthProvider } from "@/contexts/auth-context";

export const metadata: Metadata = {
  title: "PhoenixCare - AI-Powered Post-Discharge Care",
  description: "AI-powered co-pilot for post-discharge patient care, reducing 30-day readmissions through voice-enabled patient monitoring and support.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className="antialiased">
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}
