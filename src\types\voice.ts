export type CallStatus = 'idle' | 'connecting' | 'connected' | 'disconnecting' | 'failed';
export type CallType = 'patient_checkin' | 'emergency' | 'scheduled' | 'followup';
export type AudioQuality = 'poor' | 'fair' | 'good' | 'excellent';

export interface VoiceCallSession {
  id: string;
  patientId: string;
  staffId?: string; // Optional - staff can join calls
  type: CallType;
  status: CallStatus;
  startTime: Date;
  endTime?: Date;
  duration?: number; // in seconds
  audioQuality: AudioQuality;
  transcript?: string;
  summary?: string;
  aiAnalysis?: AIAnalysis;
  recordingUrl?: string;
  metadata: CallMetadata;
}

export interface CallMetadata {
  patientName: string;
  patientCondition: string;
  callPurpose: string;
  urgencyLevel: 'low' | 'medium' | 'high' | 'critical';
  scheduledTime?: Date;
  remindersSent: number;
  connectionAttempts: number;
  technicalIssues: string[];
}

export interface AIAnalysis {
  sentiment: 'positive' | 'neutral' | 'negative' | 'concerned';
  keyTopics: string[];
  symptoms: SymptomReport[];
  medicationCompliance: MedicationCompliance;
  riskAssessment: RiskAssessment;
  recommendations: string[];
  flagsForReview: string[];
  confidenceScore: number; // 0-1
}

export interface SymptomReport {
  symptom: string;
  severity: number; // 1-10 scale
  duration: string;
  frequency: string;
  triggers?: string[];
  relievingFactors?: string[];
}

export interface MedicationCompliance {
  overallCompliance: number; // 0-1
  medications: {
    name: string;
    prescribed: boolean;
    taken: boolean;
    missedDoses: number;
    sideEffects?: string[];
  }[];
}

export interface RiskAssessment {
  overallRisk: 'low' | 'medium' | 'high' | 'critical';
  factors: {
    factor: string;
    impact: 'low' | 'medium' | 'high';
    description: string;
  }[];
  recommendedActions: string[];
  urgentCare: boolean;
}

// WebRTC and Audio Types
export interface AudioDevice {
  deviceId: string;
  label: string;
  kind: 'audioinput' | 'audiooutput';
}

export interface AudioSettings {
  inputDevice?: string;
  outputDevice?: string;
  echoCancellation: boolean;
  noiseSuppression: boolean;
  autoGainControl: boolean;
  volume: number; // 0-1
  muted: boolean;
}

export interface ConnectionQuality {
  latency: number; // ms
  packetLoss: number; // percentage
  jitter: number; // ms
  bandwidth: number; // kbps
  signalStrength: 'weak' | 'fair' | 'good' | 'excellent';
}

export interface VoiceCallState {
  session: VoiceCallSession | null;
  isConnecting: boolean;
  isConnected: boolean;
  isMuted: boolean;
  isRecording: boolean;
  audioDevices: AudioDevice[];
  audioSettings: AudioSettings;
  connectionQuality: ConnectionQuality;
  transcript: string;
  error: string | null;
  staffJoined: boolean;
  aiResponding: boolean;
}

// OpenAI Realtime API Types
export interface OpenAIRealtimeConfig {
  apiKey: string;
  model: string;
  voice: 'alloy' | 'echo' | 'fable' | 'onyx' | 'nova' | 'shimmer';
  instructions: string;
  modalities: ('text' | 'audio')[];
  temperature: number;
  maxTokens?: number;
}

export interface RealtimeEvent {
  type: string;
  event_id?: string;
  [key: string]: any;
}

export interface ConversationItem {
  id: string;
  type: 'message' | 'function_call' | 'function_call_output';
  role: 'user' | 'assistant' | 'system';
  content?: Array<{
    type: 'input_text' | 'input_audio' | 'text';
    text?: string;
    audio?: string; // base64 encoded
    transcript?: string;
  }>;
  status: 'completed' | 'incomplete' | 'failed';
  timestamp: Date;
}

export interface AudioVisualization {
  isActive: boolean;
  frequencyData: Uint8Array;
  volumeLevel: number; // 0-1
  waveformData: number[];
}

// Voice Call Controls
export interface VoiceCallControls {
  startCall: (patientId: string, type: CallType) => Promise<void>;
  endCall: () => Promise<void>;
  toggleMute: () => void;
  toggleRecording: () => void;
  adjustVolume: (volume: number) => void;
  switchAudioDevice: (deviceId: string, type: 'input' | 'output') => void;
  joinAsStaff: (staffId: string) => Promise<void>;
  leaveAsStaff: () => void;
  sendTextMessage: (message: string) => void;
  requestEmergencyAssistance: () => void;
}

// Store interface for voice call management
export interface VoiceCallStore {
  state: VoiceCallState;
  controls: VoiceCallControls;
  
  // State management
  setState: (updates: Partial<VoiceCallState>) => void;
  resetState: () => void;
  
  // Audio device management
  initializeAudioDevices: () => Promise<void>;
  updateAudioSettings: (settings: Partial<AudioSettings>) => void;
  
  // Connection management
  updateConnectionQuality: (quality: Partial<ConnectionQuality>) => void;
  handleConnectionError: (error: string) => void;
  
  // Transcript management
  addTranscriptSegment: (text: string, speaker: 'user' | 'assistant' | 'staff') => void;
  clearTranscript: () => void;
  
  // Session management
  createSession: (patientId: string, type: CallType) => VoiceCallSession;
  updateSession: (updates: Partial<VoiceCallSession>) => void;
  endSession: () => void;
}

// Event types for voice call system
export type VoiceCallEvent = 
  | { type: 'CALL_STARTED'; payload: { sessionId: string; patientId: string } }
  | { type: 'CALL_CONNECTED'; payload: { sessionId: string } }
  | { type: 'CALL_ENDED'; payload: { sessionId: string; duration: number } }
  | { type: 'STAFF_JOINED'; payload: { sessionId: string; staffId: string } }
  | { type: 'STAFF_LEFT'; payload: { sessionId: string; staffId: string } }
  | { type: 'TRANSCRIPT_UPDATED'; payload: { sessionId: string; transcript: string } }
  | { type: 'AI_ANALYSIS_COMPLETE'; payload: { sessionId: string; analysis: AIAnalysis } }
  | { type: 'CONNECTION_QUALITY_CHANGED'; payload: { sessionId: string; quality: ConnectionQuality } }
  | { type: 'ERROR_OCCURRED'; payload: { sessionId: string; error: string } };
