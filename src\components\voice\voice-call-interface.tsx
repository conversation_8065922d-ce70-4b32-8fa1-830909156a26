'use client'

import { useState, useEffect, useRef } from 'react';
import { useVoiceCallStore } from '@/stores/voice-store';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Phone, 
  PhoneOff, 
  Mic, 
  MicOff, 
  Volume2, 
  VolumeX,
  Settings,
  Users,
  MessageSquare,
  AlertTriangle
} from 'lucide-react';

interface VoiceCallInterfaceProps {
  patientId: string;
  patientName: string;
  onCallEnd?: () => void;
}

export function VoiceCallInterface({ 
  patientId, 
  patientName, 
  onCallEnd 
}: VoiceCallInterfaceProps) {
  const { state, controls } = useVoiceCallStore();
  const [showTranscript, setShowTranscript] = useState(false);
  const [audioLevel, setAudioLevel] = useState(0);
  const transcriptRef = useRef<HTMLDivElement>(null);

  // Auto-scroll transcript to bottom
  useEffect(() => {
    if (transcriptRef.current) {
      transcriptRef.current.scrollTop = transcriptRef.current.scrollHeight;
    }
  }, [state.transcript]);

  // Mock audio level animation
  useEffect(() => {
    if (state.isConnected && !state.isMuted) {
      const interval = setInterval(() => {
        setAudioLevel(Math.random() * 100);
      }, 100);
      return () => clearInterval(interval);
    } else {
      setAudioLevel(0);
    }
  }, [state.isConnected, state.isMuted]);

  const handleStartCall = async () => {
    try {
      await controls.startCall(patientId, 'patient_checkin');
    } catch (error) {
      console.error('Failed to start call:', error);
    }
  };

  const handleEndCall = async () => {
    try {
      await controls.endCall();
      onCallEnd?.();
    } catch (error) {
      console.error('Failed to end call:', error);
    }
  };

  const getCallStatusColor = () => {
    if (state.isConnecting) return 'bg-yellow-500';
    if (state.isConnected) return 'bg-green-500';
    if (state.error) return 'bg-red-500';
    return 'bg-gray-500';
  };

  const getCallStatusText = () => {
    if (state.isConnecting) return 'Connecting...';
    if (state.isConnected) return 'Connected';
    if (state.error) return 'Error';
    return 'Idle';
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const [callDuration, setCallDuration] = useState(0);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (state.isConnected && state.session) {
      interval = setInterval(() => {
        const now = new Date();
        const duration = Math.floor((now.getTime() - state.session!.startTime.getTime()) / 1000);
        setCallDuration(duration);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [state.isConnected, state.session]);

  return (
    <div className="space-y-4">
      {/* Call Status Header */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className={`w-3 h-3 rounded-full ${getCallStatusColor()}`} />
              <div>
                <CardTitle className="text-lg">{patientName}</CardTitle>
                <p className="text-sm text-muted-foreground">
                  {getCallStatusText()}
                  {state.isConnected && ` • ${formatDuration(callDuration)}`}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {state.staffJoined && (
                <Badge variant="secondary" className="flex items-center space-x-1">
                  <Users className="w-3 h-3" />
                  <span>Staff Joined</span>
                </Badge>
              )}
              {state.isRecording && (
                <Badge variant="destructive" className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
                  <span>Recording</span>
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Audio Visualization */}
      {state.isConnected && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-center space-x-2 h-16">
              {/* Simple audio level visualization */}
              {Array.from({ length: 20 }).map((_, i) => (
                <div
                  key={i}
                  className={`w-2 bg-primary transition-all duration-100 ${
                    audioLevel > (i * 5) ? 'opacity-100' : 'opacity-20'
                  }`}
                  style={{
                    height: `${Math.max(4, (audioLevel / 100) * 64)}px`,
                  }}
                />
              ))}
            </div>
            <div className="text-center text-sm text-muted-foreground mt-2">
              {state.aiResponding ? 'AI is speaking...' : 'Listening...'}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Call Controls */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center space-x-4">
            {!state.isConnected ? (
              <Button
                onClick={handleStartCall}
                disabled={state.isConnecting}
                size="lg"
                className="bg-green-600 hover:bg-green-700 text-white px-8"
              >
                <Phone className="w-5 h-5 mr-2" />
                {state.isConnecting ? 'Connecting...' : 'Start Call'}
              </Button>
            ) : (
              <>
                {/* Mute Toggle */}
                <Button
                  onClick={controls.toggleMute}
                  variant={state.isMuted ? "destructive" : "outline"}
                  size="lg"
                >
                  {state.isMuted ? <MicOff className="w-5 h-5" /> : <Mic className="w-5 h-5" />}
                </Button>

                {/* Volume Control */}
                <Button
                  onClick={() => controls.adjustVolume(state.audioSettings.volume > 0 ? 0 : 0.8)}
                  variant="outline"
                  size="lg"
                >
                  {state.audioSettings.volume > 0 ? 
                    <Volume2 className="w-5 h-5" /> : 
                    <VolumeX className="w-5 h-5" />
                  }
                </Button>

                {/* Recording Toggle */}
                <Button
                  onClick={controls.toggleRecording}
                  variant={state.isRecording ? "destructive" : "outline"}
                  size="lg"
                >
                  <div className={`w-3 h-3 rounded-full ${state.isRecording ? 'bg-white' : 'bg-red-500'}`} />
                </Button>

                {/* Transcript Toggle */}
                <Button
                  onClick={() => setShowTranscript(!showTranscript)}
                  variant={showTranscript ? "default" : "outline"}
                  size="lg"
                >
                  <MessageSquare className="w-5 h-5" />
                </Button>

                {/* Settings */}
                <Button variant="outline" size="lg">
                  <Settings className="w-5 h-5" />
                </Button>

                {/* End Call */}
                <Button
                  onClick={handleEndCall}
                  variant="destructive"
                  size="lg"
                  className="px-8"
                >
                  <PhoneOff className="w-5 h-5 mr-2" />
                  End Call
                </Button>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Connection Quality */}
      {state.isConnected && (
        <Card>
          <CardContent className="pt-4">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-4">
                <div>
                  <span className="text-muted-foreground">Quality: </span>
                  <span className={`font-medium ${
                    state.connectionQuality.signalStrength === 'excellent' ? 'text-green-600' :
                    state.connectionQuality.signalStrength === 'good' ? 'text-blue-600' :
                    state.connectionQuality.signalStrength === 'fair' ? 'text-yellow-600' :
                    'text-red-600'
                  }`}>
                    {state.connectionQuality.signalStrength}
                  </span>
                </div>
                <div>
                  <span className="text-muted-foreground">Latency: </span>
                  <span className="font-medium">{state.connectionQuality.latency}ms</span>
                </div>
              </div>
              {state.error && (
                <div className="flex items-center space-x-1 text-red-600">
                  <AlertTriangle className="w-4 h-4" />
                  <span className="text-xs">{state.error}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Transcript */}
      {showTranscript && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Conversation Transcript</CardTitle>
          </CardHeader>
          <CardContent>
            <div 
              ref={transcriptRef}
              className="h-64 overflow-y-auto bg-gray-50 p-4 rounded-md text-sm font-mono whitespace-pre-wrap"
            >
              {state.transcript || 'Transcript will appear here during the call...'}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
